package main

import (
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"testing"
	"time"
	"turnero/internal/app/appointment"
	"turnero/internal/config"
	"turnero/internal/domain"
	mockAcuity "turnero/internal/infra/acuity/mocks"
	mockDB "turnero/internal/infra/database/mocks"

	"gopkg.cabify.tools/servant"
)

func TestMainServer(t *testing.T) {
	os.Setenv("TURNERO_BASEURL", "base")
	conf, err := config.GetConfig()
	if err != nil {
		t.Fatalf("Failed to get config: %v", err)
	}
	// Start the server
	srv := servant.NewService()
	mockDBClient := new(mockDB.DatabaseClient)
	mockDBClient.On("GetTabletDataByAgentEmail", "<EMAIL>").Return(domain.TabletData{Stand: "testStand", ContactReasons: make([]domain.ContactReason, 0)}, nil)
	mockAcuityClient := new(mockAcuity.AcuityClient)
	StartServer(srv, conf, mockDBClient, &appointment.AppointmentManager{}, mockAcuityClient)

	// Wait for the server to start
	time.Sleep(2 * time.Second)

	// Subtest for checking the /status endpoint
	t.Run("Check /status endpoint", func(t *testing.T) {
		resp, err := http.Get("http://localhost:8080/status")
		if err != nil {
			t.Fatalf("Failed to make GET request to /status: %v", err)
		}
		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code 200, got %v", resp.StatusCode)
		}
	})

	// Subtest for checking the BaseURL endpoint
	t.Run("Check BaseURL tablet endpoint", func(t *testing.T) {
		req, err := http.NewRequest("GET", "http://localhost:8080/base/tablet", nil)
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("X-Email", "<EMAIL>")
		client := &http.Client{}

		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make GET request to /base/tablet: %v", err)
		}
		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code 200 for /base/tablet, got %v", resp.StatusCode)
		}
	})
	// Send shutdown signal to the server
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT) // Catch SIGINT
	stop <- syscall.SIGINT              // Send SIGINT signal
}
