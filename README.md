# Turnero



## Project scope

Turnero it's the WebApp to manage the turn of the customers for offline attention. It's connected to Zendesk and to the Cabify SOTs to allow a better attention and metrics tracking.

## Zendesk Configurations
- Sandbox:
  - [close webhook](https://cabify1668164776.zendesk.com/admin/apps-integrations/actions-webhooks/webhooks/01JDHFV6568ZAPCJ0ERVYJ5X4V/details) 
  - [close trigger](https://cabify1668164776.zendesk.com/admin/objects-rules/rules/triggers/22849450633746)
- Production:
  - [close webhook](https://cabify.zendesk.com/admin/apps-integrations/actions-webhooks/webhooks/01JH0WVW2BVM80HHFT90CG4RGY/details)
  - [close trigger](https://cabify.zendesk.com/admin/objects-rules/rules/triggers/23769678012946)
## Local development

### Requirements
- GO 1.22
- [Templ](https://templ.guide/quick-start/installation)
- [AIR](https://github.com/air-verse/air) 1.61.1 (recommended to automatically reload the server and rebuild the templates)]
### Setup

1. Clone the repository
2. On project directory run the service with:
```bash
   go run main.go
```
Or with hot reload:
```bash
   air
```

## Technologies
- GO 1.22
- HTMX 2.0.3
- HTMX-ext-sse 2.2.2
- Apine 3.14.3
- SCSS
- [Templ](https://templ.guide/) 

## Business Logic
- Every Country have Stands. 
- Every Stand have one Line, one Tablet and could have up to 6 Desks.
- Every Desk should have an agent assigned to can attend turns.
- Desks are displayed on Line view only if they have an agent assigned.
- Agents could select his own desks, between the ones that are free on his Stand.

![diagram](https://gitlab.otters.xyz/platform/business-automation/turnero/-/raw/master/docs/img/bdiagram.png)

## Maintenance 
- To add or edit a country, stand, or agent, edit the file on `data/databaseToPopulate.json` (or `developmentData.json` for local or KTE) and make a MR to the repository.
- All employees under @cabify.com or @external.cabify.com can access the [landing](https://tools.cabify.com/turnero) but only the configured ones on it database can access to the subpaths.
- TURNERO_POPULATE env variable of ARGO Manifest should be set to true to populate the database with the data on the file.

## Monitoring
- There is a [dashboard](https://grafana.cabify.services/d/aeb8ttm1grjlsc/turnero?orgId=1&from=now-7d&to=now&timezone=UTC) on Grafana to monitor the Agents working hours.

## Project status
On production, ready to use. 

## Useful links
- [Landing](https://tools.cabify.com/turnero)
- [Public Landing (only lines views)](https://tools.cabify.com/turnero/stand-line)
- [Agents Manual](https://docs.google.com/document/d/1rFuRmOLoYXIilwZqK1rMkQqZ6GdZe-KjjXJnwPN0kSI/edit?tab=t.0)
- [Slideshow](https://docs.google.com/presentation/d/1nlbMoxdLj5YxGj_QHk0KQjr_0i-ITJoilmIWwr98qZk/edit#slide=id.g2e7d59b9641_0_9868)