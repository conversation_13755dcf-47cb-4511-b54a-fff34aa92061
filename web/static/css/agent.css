/* Agent Panel Styles - Modern Design matching Admin Panel */

/* ===== GLOBAL AGENT PANEL STYLES ===== */

/* Override body styles for agent panel */
.agent-panel body {
    font-family: 'CabifyCircularBook', 'Cabify Circular Book', sans-serif !important;
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1)) !important;
    background: linear-gradient(135deg, #9796f0 0%, #fbc7d4 100%);
    min-height: 100vh;
}

.agent-panel h1, .agent-panel h2, .agent-panel h3, .agent-panel h4, .agent-panel h5, .agent-panel h6 {
    font-family: 'CabifyCircularBold', 'Cabify Circular Bold', sans-serif !important;
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1)) !important;
}

/* ===== AGENT NAVBAR STYLES ===== */

.agent-navbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(110, 74, 190, 0.15);
    border-radius: 0 0 1rem 1rem;
    margin-bottom: 1.5rem;
}

/* Logo Styles - Enhanced */
.agent-logo {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.agent-logo img {
    height: 2.5rem;
    width: auto;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(110, 74, 190, 0.2));
}

.agent-logo .logo-text {
    font-family: 'CabifyCircularBold', 'Cabify Circular Bold', sans-serif !important;
    font-size: 1.75rem;
    font-weight: 700;
    color: #6e4abe !important; /* moradul color for logo text */
    margin-left: 0.5rem;
    text-shadow: 0 2px 4px rgba(110, 74, 190, 0.1);
    transition: all 0.3s ease;
}

.agent-logo:hover {
    transform: translateY(-1px);
    text-decoration: none;
}

.agent-logo:hover img {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 8px rgba(110, 74, 190, 0.3));
}

.agent-logo:hover .logo-text {
    color: #7145d6 !important;
    text-shadow: 0 4px 8px rgba(110, 74, 190, 0.2);
}

/* Agent Info Section */
.agent-info-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.agent-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.1) 0%, rgba(151, 150, 240, 0.05) 100%);
    border-radius: 0.75rem;
    border: 1px solid rgba(110, 74, 190, 0.2);
    transition: all 0.3s ease;
}

.agent-info-item:hover {
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.15) 0%, rgba(151, 150, 240, 0.1) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(110, 74, 190, 0.2);
}

.agent-info-item i {
    color: #6e4abe;
    font-size: 1rem;
}

.agent-info-text {
    font-weight: 600;
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1)) !important;
    margin: 0;
    font-size: 0.9rem;
}

/* Desk Selection Styling */
.agent-desk-select {
    min-width: 150px;
}

.agent-desk-select .form-select {
    border: 2px solid rgba(110, 74, 190, 0.3);
    border-radius: 0.75rem;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.agent-desk-select .form-select:focus {
    border-color: #6e4abe;
    box-shadow: 0 0 0 0.25rem rgba(110, 74, 190, 0.15);
    background: white;
}

/* Logout Button Styling */
.agent-logout-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.agent-logout-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.agent-logout-btn i {
    color: white;
    font-size: 1rem;
}

/* ===== MAIN CONTENT CONTAINER ===== */

.agent-main-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 1.5rem;
    box-shadow: 
        0 10px 30px rgba(110, 74, 190, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(110, 74, 190, 0.1);
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    max-width: 95%;
}

.agent-main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6e4abe 0%, #9796f0 50%, #6e4abe 100%);
    z-index: 1;
}

/* ===== ALERT STYLES ===== */

.agent-alert {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.agent-alert.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.agent-alert.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

/* ===== ENHANCED BUTTON STYLES ===== */

/* Primary buttons with enhanced styling */
.agent-panel .btn-primary {
    background: linear-gradient(135deg, #7145d6 0%, #9796f0 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(113, 69, 214, 0.3);
    color: white !important;
}

.agent-panel .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.agent-panel .btn-primary:hover::before {
    left: 100%;
}

.agent-panel .btn-primary:hover {
    background: linear-gradient(135deg, #9796f0 0%, #7145d6 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(113, 69, 214, 0.4);
}

/* Success buttons */
.agent-panel .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: white !important;
}

.agent-panel .btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

/* Warning buttons */
.agent-panel .btn-warning {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(110, 74, 190, 0.2);
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(110, 74, 190, 0.2);
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1)) !important;
}

.agent-panel .btn-warning:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(110, 74, 190, 0.3);
}

/* Danger buttons */
.agent-panel .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    color: white !important;
}

.agent-panel .btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
}

/* Info buttons */
.agent-panel .btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    color: white !important;
}

.agent-panel .btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(23, 162, 184, 0.4);
}

/* Small buttons */
.agent-panel .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Large buttons */
.agent-panel .btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 700;
}

/* Disabled button state */
.agent-panel .btn:disabled {
    opacity: 0.6;
    transform: none !important;
    box-shadow: none !important;
    cursor: not-allowed;
}

/* ===== REQUESTER INFO SECTION ===== */

.requester-info-card {
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.05) 0%, rgba(151, 150, 240, 0.02) 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(110, 74, 190, 0.1);
    box-shadow: 0 4px 12px rgba(110, 74, 190, 0.1);
    margin-bottom: 1rem;
}

.requester-info-title {
    color: #6e4abe;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.requester-info-title i {
    font-size: 1.2rem;
}

.requester-info-item {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.requester-info-label {
    font-weight: 600;
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1));
    min-width: 80px;
}

.requester-info-value {
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1));
    flex: 1;
}

/* Action buttons in requester section */
.requester-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(110, 74, 190, 0.1);
}

/* ===== APPOINTMENT MANAGEMENT SECTION ===== */

.appointment-management-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(110, 74, 190, 0.1);
    box-shadow: 0 4px 12px rgba(110, 74, 190, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.status-badge.attending {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.status-badge.pending {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1));
    box-shadow: 0 2px 8px rgba(110, 74, 190, 0.2);
    border: 1px solid rgba(110, 74, 190, 0.2);
}

.status-badge i {
    font-size: 1rem;
}

/* Caller section */
.caller-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.05) 0%, rgba(151, 150, 240, 0.02) 100%);
    border-radius: 1rem;
    border: 1px solid rgba(110, 74, 190, 0.1);
}

.line-number {
    font-size: 3rem;
    font-weight: 700;
    color: #6e4abe;
    text-shadow: 0 2px 4px rgba(110, 74, 190, 0.2);
    margin: 0;
}

.call-button {
    min-width: 120px;
}

/* ===== APPOINTMENT LIST SECTION ===== */

.appointment-list-container {
    flex-grow: 1;
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.02) 0%, rgba(151, 150, 240, 0.01) 100%);
    border-radius: 1rem;
    padding: 1rem;
    border: 1px solid rgba(110, 74, 190, 0.1);
    margin-bottom: 1rem;
    scroll-behavior: smooth;
}

.appointment-list-title {
    color: #6e4abe;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.appointment-list-title i {
    font-size: 1.2rem;
}

/* Line list styling */
.line-list-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 0.75rem;
    border: 1px solid rgba(110, 74, 190, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

/* Compact styling for agent view only */
.agent-panel .line-list-item {
    padding: 0.4rem;
    margin-bottom: 0.3rem;
}

/* Compact font sizes for agent view */
.agent-panel .line-list-item h2 {
    font-size: 2rem !important;
    margin-bottom: 0 !important;
}

.agent-panel .line-list-item h4 {
    font-size: 1.2rem !important;
    margin-bottom: 0 !important;
}

.line-list-item:hover {
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.05) 0%, rgba(151, 150, 240, 0.02) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(110, 74, 190, 0.15);
}

.line-list-item.first-item {
    background: linear-gradient(135deg, #6e4abe 0%, #9796f0 100%);
    color: white !important;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(110, 74, 190, 0.3);
}

.line-list-item.first-item:hover {
    background: linear-gradient(135deg, #9796f0 0%, #6e4abe 100%);
    color: white !important;
}

.line-list-item.first-item h2,
.line-list-item.first-item h4 {
    color: white !important;
}

.line-list-item.ready-to-call {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 1));
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(110, 74, 190, 0.2);
    border: 1px solid rgba(110, 74, 190, 0.2);
}

/* Clickable appointment styling */
.clickable-appointment {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.clickable-appointment::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.clickable-appointment:hover::before {
    left: 100%;
}

.clickable-appointment:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(110, 74, 190, 0.25);
}

.clickable-appointment.first-item:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 20px rgba(110, 74, 190, 0.4);
    color: white !important;
}

.clickable-appointment.first-item h2,
.clickable-appointment.first-item h4 {
    color: white !important;
}

.clickable-appointment.ready-to-call:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 20px rgba(110, 74, 190, 0.3);
}

/* Disabled state when agent is on call */
.clickable-appointment.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    pointer-events: none;
}

.clickable-appointment.disabled:hover {
    transform: none;
    box-shadow: none;
}

.clickable-appointment.disabled::before {
    display: none;
}

/* Active/selected appointment styling */
.clickable-appointment.selected {
    background: linear-gradient(135deg, #6e4abe 0%, #9796f0 100%);
    color: white !important;
    font-weight: 700;
    box-shadow: 0 6px 16px rgba(110, 74, 190, 0.4);
    border: 2px solid #9796f0;
    scroll-margin-top: 2rem;
    scroll-margin-bottom: 2rem;
}

.clickable-appointment.selected:hover {
    background: linear-gradient(135deg, #9796f0 0%, #6e4abe 100%);
    color: white !important;
}

.clickable-appointment.selected h2,
.clickable-appointment.selected h4 {
    color: white !important;
}

.clickable-appointment.first-item.selected {
    background: linear-gradient(135deg, #6e4abe 0%, #9796f0 100%);
    color: white !important;
    border: 2px solid #9796f0;
    scroll-margin-top: 2rem;
    scroll-margin-bottom: 2rem;
}

.clickable-appointment.first-item.selected:hover {
    background: linear-gradient(135deg, #9796f0 0%, #6e4abe 100%);
    color: white !important;
}

/* Auto-scroll to selected appointment using CSS */
.line-list-item.ready-to-call {
    scroll-margin-top: 2rem;
    scroll-margin-bottom: 2rem;
}

/* ===== NAVIGATION CONTROLS ===== */

.navigation-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(110, 74, 190, 0.1);
}

.navigation-left {
    display: flex;
    gap: 0.75rem;
}

.navigation-right {
    margin-left: auto;
}

/* ===== FOOTER SECTION ===== */

.agent-footer {
    text-align: center;
    padding: 1rem;
    margin-top: 1rem;
    border-top: 1px solid rgba(110, 74, 190, 0.1);
    background: linear-gradient(135deg, rgba(110, 74, 190, 0.02) 0%, rgba(151, 150, 240, 0.01) 100%);
    border-radius: 0 0 1.5rem 1.5rem;
}

.agent-footer p {
    margin: 0;
    color: rgba(var(--bs-moradul-dark-m800-rgb), var(--bs-text-opacity, 0.7));
    font-size: 0.875rem;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .agent-navbar .container > div {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .agent-info-section {
        justify-content: center;
        flex-direction: column;
        gap: 0.5rem;
    }

    .agent-main-container {
        max-width: 98%;
        margin: 0.5rem auto;
    }

    .caller-section {
        flex-direction: column;
        text-align: center;
    }

    .line-number {
        font-size: 2.5rem;
    }

    .navigation-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .navigation-left {
        justify-content: center;
        width: 100%;
    }

    .navigation-right {
        margin-left: 0;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .requester-actions {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .agent-logo .logo-text {
        font-size: 1.5rem;
    }

    .agent-info-item {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .line-number {
        font-size: 2rem;
    }

    .agent-panel .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .agent-panel .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* ===== ANIMATIONS AND TRANSITIONS ===== */

@keyframes pulse-glow {
    from {
        box-shadow: 0 4px 12px rgba(110, 74, 190, 0.3);
    }
    to {
        box-shadow: 0 6px 20px rgba(110, 74, 190, 0.5);
    }
}

.status-badge.attending {
    animation: pulse-glow 2s ease-in-out infinite alternate;
}

/* Smooth transitions for all interactive elements */
.agent-panel * {
    transition: all 0.3s ease;
}

/* Focus states for accessibility */
.agent-panel .btn:focus,
.agent-panel .form-select:focus,
.agent-panel .form-control:focus {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(110, 74, 190, 0.25);
}
