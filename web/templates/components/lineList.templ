package components

import (
    "strconv"
    "time"
    "turnero/internal/domain"
)

templ LineList(appointments []domain.Appointment, baseURL string) {
    <div class="row">
        for _, appointment := range getNotAttendedAppointments(appointments) {
            <div class="col-12">
                if isAgentView(baseURL) {
                    <div id={"appointment-" + strconv.Itoa(appointment.ID)}
                         class={"line-list-item clickable-appointment" + addSelectedClassIfIsReadyToCall(appointment.Status)}
                         hx-post={baseURL + "/partials/agent/select-appointment-direct"}
                         hx-target="#content"
                         hx-vals={`{"appointment_id": "` + strconv.Itoa(appointment.ID) + `"}`}
                         x-bind:class={"onCall ? 'disabled' : (selectedAppointment && selectedAppointment.id == " + strconv.Itoa(appointment.ID) + " ? 'selected' : '')"}
                         data-bs-toggle="tooltip"
                         title="Click para seleccionar esta cita">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                if isAcuityAppointment(appointment) {
                                    <i class="fa-solid fa-calendar-days text-primary me-2" title="Cita programada"></i>
                                }
                                if appointment.Status == domain.AttentionReadyToCall {
                                    <h2 class="display-3 mb-0">{ appointment.LineID }</h2>
                                } else {
                                    <h4 class="mb-0">{ appointment.LineID }</h4>
                                }
                            </div>
                            if isAcuityAppointment(appointment) {
                                <div class="appointment-datetime text-muted">
                                    <small>{ formatPlannedDateTime(appointment.PlannedAt) }</small>
                                </div>
                            }
                        </div>
                    </div>
                } else {
                    <div class={"line-list-item" + addSelectedClassIfIsReadyToCall(appointment.Status)}>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                if isAcuityAppointment(appointment) {
                                    <i class="fa-solid fa-calendar-days text-primary me-2" title="Cita programada"></i>
                                }
                                if appointment.Status == domain.AttentionReadyToCall {
                                    <h2 class="display-3 mb-0">{ appointment.LineID }</h2>
                                } else {
                                    <h4 class="mb-0">{ appointment.LineID }</h4>
                                }
                            </div>
                            if isAcuityAppointment(appointment) {
                                <div class="appointment-datetime text-muted">
                                    <small>{ formatPlannedDateTime(appointment.PlannedAt) }</small>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        }
    </div>
}

func getNotAttendedAppointments(appointments []domain.Appointment) []domain.Appointment {
    var createdAppointments []domain.Appointment
    for _, appointment := range appointments {
        if appointment.Status == domain.AttentionCreated || appointment.Status == domain.AttentionReadyToCall{
            createdAppointments = append(createdAppointments, appointment)
        }
    }
    return createdAppointments
}

func addSelectedClassIfIsReadyToCall(status domain.AttentionStatus) string {
    if status == domain.AttentionReadyToCall {
        return " ready-to-call"
    }
    return ""
}

func isAgentView(baseURL string) bool {
    // Check if this is being called from an agent view (has agent endpoints)
    return baseURL != "" && len(baseURL) > 0
}

// isAcuityAppointment checks if an appointment is from Acuity
func isAcuityAppointment(appointment domain.Appointment) bool {
    return appointment.Origin == "acuity"
}

// formatPlannedDateTime formats the planned date and time for display
func formatPlannedDateTime(plannedAt time.Time) string {
    return plannedAt.Format("02/01 15:04")
}