package templates

import (
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"time"
	"turnero/internal/domain"
	"turnero/web/templates/components"

	log "github.com/sirupsen/logrus"
)

const MAX_DESKS = 6

func generateDeskSelects(selectedDesks []int) []components.FormField {
	var fields []components.FormField
	desk := components.FormField{
		Label:       "",
		Type:        "select",
		ID:          "desk",
		Name:        "desk",
		Placeholder: "Selecciona una mesa",
		Options:     components.OptionSlice{},
	}
	for i := range MAX_DESKS {
		deskNumber := i + 1
		if !slices.Contains(selectedDesks, deskNumber) {
			deskStr := strconv.Itoa(deskNumber)
			desk.Options = append(desk.Options, components.Option{
				Key:   deskStr,
				Value: "Mesa " + deskStr,
				// Category: "", // Opcional, si necesitas categorizar las mesas
			})
		}
	}

	fields = append(fields, desk)
	return fields
}

func getCalledAppointment(appointments []domain.Appointment, agentID int) *domain.Appointment {
	for _, appointment := range appointments {
		if appointment.Status == domain.AttentionCalled && appointment.AgentID == agentID {
			return &appointment
		}
	}
	return nil
}

// getNotAttendedAppointments returns the selected appointment or the first one if there is no selected one
func getPreCallAppointment(appointments []domain.Appointment) *domain.Appointment {
	// First, look for appointments with ready_to_call status
	for _, appointment := range appointments {
		if appointment.Status == domain.AttentionReadyToCall && appointment.AgentID == 0 {
			return &appointment
		}
	}

	// If no ready_to_call appointment found, return the first one
	if len(appointments) > 0 {
		return &appointments[0]
	}

	return nil
}

func parseAppointmentToAlpineMap(selectedAppointment *domain.Appointment) string {
	if selectedAppointment == nil {
		return ""
	}

	return fmt.Sprintf("{id: '%d', line_id: '%s', requester: { user_id: '%s', email: '%s', name: '%s', surname: '%s', segment: '%s' }, contact_reason: {id : '%d', name: '%s', audience: '%s'}, created_at: '%s', zendesk_ticket_id: '%d', stand_id: '%d', agent_id: '%d', status: '%s', last_do: '%s' }",
		selectedAppointment.ID,
		selectedAppointment.LineID,
		selectedAppointment.Requester.UserID,
		selectedAppointment.Requester.Email,
		selectedAppointment.Requester.Name,
		selectedAppointment.Requester.Surname,
		selectedAppointment.Requester.Segment,
		selectedAppointment.ContactReason.ID,
		selectedAppointment.ContactReason.Name,
		selectedAppointment.ContactReason.Audience,
		selectedAppointment.CreatedAt.Format(time.RFC3339),
		selectedAppointment.ZendeskTicketID,
		selectedAppointment.StandID,
		selectedAppointment.AgentID,
		selectedAppointment.Status.String(),
		selectedAppointment.LastDO.Format(time.RFC3339),
	)
}

func parseDataToJSONString(uiData domain.AgentUIData) string {
	uiDataJson, err := json.Marshal(uiData)
	if err != nil {
		log.Errorf("Error marshalling appointments to JSON: %v", err)
		return ""
	}
	return string(uiDataJson)
}
