// @title Turnero API
// @version 1.0
// @description API REST para gestión de agentes, stands y contact reasons del sistema Turnero. La autenticación se maneja a nivel de nginx con OAuth.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /

package main

import (
	"database/sql"
	"os"
	"os/signal"
	"syscall"
	"time"
	"turnero/internal/app/appointment"
	"turnero/internal/app/turns"
	"turnero/internal/config"
	GRPCClients "turnero/internal/infra/GRPC"
	"turnero/internal/infra/acuity"
	"turnero/internal/infra/api"
	"turnero/internal/infra/database"
	"turnero/internal/infra/featureStore"
	"turnero/internal/infra/mail"
	"turnero/internal/infra/schedulerPlanner"
	"turnero/internal/infra/zendesk"

	"gopkg.cabify.tools/servant"
	"gopkg.cabify.tools/zdgo"

	"github.com/go-co-op/gocron/v2"
	log "github.com/sirupsen/logrus"

	_ "turnero/docs" // This line is necessary for go-swagger to find your docs!
)

func StartServer(server *servant.Service, conf *config.Config, dbClient database.DatabaseClient, appointmentManager appointment.LineFlowManager, acuityClient acuity.AcuityClient) {
	server.AddController(api.NewRouter(conf.BaseURL, os.Getenv("ZDGO_ZENDESKURL"), conf.AdminURL, dbClient, appointmentManager, acuityClient, conf.Acuity.WebhookSecret, conf.ZendeskIncomingRequestsToken))
	go server.Run()
	log.Println("Server is running...")
}

func populateDatabase(jsonPath string, db *sql.DB) error {
	jsonData, err := os.ReadFile(jsonPath)
	if err != nil {
		return err
	}

	err = database.InsertDataFromJSON(db, jsonData)
	if err != nil {
		return err
	}
	return nil
}

func main() {
	conf, err := config.GetConfig()
	if err != nil {
		log.Fatalf("Failed to get config: %v", err)
	}
	srv := servant.NewService()

	mysql := srv.MySQL()
	_, err = database.RunMigrations(conf.DB)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer mysql.Close()

	// Prepare testing environment
	if conf.Populate {
		log.Infof("Populating database...")
		err := populateDatabase(conf.PopulateFile, mysql)
		if err != nil {
			log.Fatalf("Failed to populate database: %v", err)
		}
		log.Printf("Database populated with %s", conf.PopulateFile)
	}

	// Instantiate clients
	dbClient := database.NewClient(mysql)
	httpClient := srv.HTTPClient()
	grpcClients, grpcConnections, err := GRPCClients.InitGRPCConnections(srv, conf.UsersConnection)
	if err != nil {
		log.Fatalf("Failed to init GRPC connections: %v", err)
	}
	defer GRPCClients.CloseGRPCConnections(grpcConnections)
	usersClient := GRPCClients.NewUsersClient(grpcClients.UsersClient)

	zdGOClient, err := zdgo.NewService()
	if err != nil {
		log.Fatalf("Failed to create ZDGO Zendesk client: %v", err)
	}
	fStoreClient := featureStore.NewFeatureStoreClient(featureStore.NewFeatureStoreHandler(conf.FeatureStore, httpClient))

	mailgunHandler := mail.NewMailGunHandler(mail.NewSmtpMailSender(), conf.Mailgun)
	mailClient := mail.NewMailClient(mailgunHandler, conf.Mailgun)

	// Initialize Acuity client

	acuityClient := acuity.NewClient(acuity.NewHTTPHandler(httpClient, conf.Acuity), conf.Acuity)

	// Generate APPs
	appointmentManager := appointment.NewAppointmentManager(dbClient, zendesk.NewClient(zdGOClient, conf.CustomFields), fStoreClient, usersClient, mailClient)
	workingTurnsManager := turns.NewTurnManager(dbClient)

	// Schedule jobs
	turnsResetJob := schedulerPlanner.JobConfig{
		Name: "Reset turns",
		Cron: "0 3 * * *",
		Task: func() {
			log.Infof("Resetting turns at %v", time.Now())
			err := workingTurnsManager.CloseAllTurns()
			if err != nil {
				log.Errorf("Failed to reset turns: %v", err)
			}
		},
	}
	planner, err := schedulerPlanner.NewScheduler(gocron.WithLocation(time.UTC))
	if err != nil {
		log.Fatalf("Failed to create scheduler planner: %v", err)
	}
	err = planner.SetupScheduledJobs(turnsResetJob)
	if err != nil {
		log.Fatalf("Failed to setup scheduled jobs: %v", err)
	}

	StartServer(srv, conf, dbClient, appointmentManager, acuityClient)

	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)

	<-stop
	log.Println("Shutting down the server...")
	srv.Shutdown() // Graceful shutdown
}
