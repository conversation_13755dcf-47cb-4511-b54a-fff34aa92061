# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# IDE/Editor folders
.idea
.vscode
*.swp
*.swo

# macOS generated files
.DS_Store

# GoLand generated files
/.idea
/idea

# Secret or sensitive files
/.env
.env

# testing residuals
coverage/
.test_coverage.txt
/estimado_examples/
/mysql-data/
/credentials.json
tmp/
/node_modules/
/docs/Turnero API.paw
/turnero
