#!/bin/bash
# vim: ai:ts=8:sw=8:noet
# Check shell scripts
# Intended to be run from local machine or CI
set -eufo pipefail
export SHELLOPTS	# propagate set to children by default
IFS=$'\t\n'

# Check required commands are in place
command -v shellcheck >/dev/null 2>&1 || { echo 'please install shellcheck or use image that has it'; exit 1; }

# shellcheck disable=SC1090
source <(set +f; cat /usr/local/lib/functionarium/*.sh || { >&2 echo "Please install https://gitlab.otters.xyz/product/systems/functionarium#install"; echo "exit 42"; })
[[ "true" == "${GITLAB_CI:-false}" ]] && trap ci_shred_secrets EXIT

linter_shellcheck .
