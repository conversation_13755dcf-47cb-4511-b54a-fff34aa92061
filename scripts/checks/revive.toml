ignoreGeneratedHeader = false
severity = "error"
confidence = 0.8
errorCode = 1
warningCode = 0

[rule.atomic]
[rule.blank-imports]
[rule.confusing-naming]
[rule.confusing-results]
[rule.constant-logical-expr]
[rule.context-as-argument]
[rule.context-keys-type]
[rule.cyclomatic]
[rule.deep-exit]
[rule.dot-imports]
[rule.duplicated-imports]
[rule.empty-block]
[rule.empty-lines]
[rule.error-naming]
[rule.error-return]
[rule.error-strings]
[rule.errorf]
[rule.get-return]
[rule.if-return]
[rule.increment-decrement]
[rule.indent-error-flow]
[rule.modifies-value-receiver]
[rule.range]
[rule.range-val-in-closure]
[rule.receiver-naming]
[rule.redefines-builtin-id]
[rule.struct-tag]
[rule.superfluous-else]
[rule.time-naming]
[rule.var-naming]
    severity = "warning"
[rule.declared-return]
[rule.var-declaration]
[rule.unexported-return]
[rule.unhandled-error]
[rule.unnecessary-stmt]
[rule.unreachable-code]
[rule.unused-parameter]
[rule.unused-receiver]
[rule.waitgroup-by-value]
