stages:
  - check and compile
  - build
  - security
  - publish
  - deploy
  - backstage

default:
  tags:
    - k8s-aws

include:
  - project: platform/dev-x/comms/developer-platform/backstage-utils
    ref: master
    file: templates/ci/backstage.yaml

services:
  - name: docker:dind
    command: ["--tls=false"]

variables:
  GOOS: linux
  GOARCH: amd64
  DOCKER_DRIVER: overlay2
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""
  GOPROXY: "http://athens-proxy.athens-proxy.svc.cluster.local"
  GONOSUMDB: "gopkg.cabify.tools/*,gitlab.otters.xyz:5005/*,secondary.gitlab.otters.xyz:5005/*,gitlab.com/cabify/*,github.com/cabify/*"

.docker:
  image: gitlab.otters.xyz:5005/infrastructure/gitlab/dockerfiles/docker-ci-builder/docker-ci-builder:latest
  services:
    - docker:dind
  before_script:
    - gcloud auth activate-service-account --key-file=/secrets/googlecloud-credentials/credentials.json
    - source /usr/local/lib/functionarium/wait_for_it.sh
    - wait_for_it docker:2375 180
    - bash ./scripts/docker-login.sh

.compile:
  image: registry.cabify.services/product/guild/go/dockerfiles/docker-ci-golang-debian/docker-ci-golang-debian:latest
  before_script:
    - echo -e "machine github.com\n\tlogin $GITHUB_USERNAME\n\tpassword $GITHUB_TOKEN" >> $HOME/.netrc
    - echo -e "machine gitlab.otters.xyz:5005\n\tlogin gitlab-ci-token\n\tpassword $CI_JOB_TOKEN" >> $HOME/.netrc

compile:
  stage: check and compile
  extends: .compile
  artifacts:
    paths:
      - $CI_PROJECT_DIR/bin
    expire_in: 1 hour
  script:
    - bash ./scripts/compile.sh

check:
  stage: check and compile
  extends: .compile
  retry: 1
  dependencies: []
  script:
    - bash ./scripts/check-ci.sh
  coverage: '/^Total test coverage: \d+.\d+\%$/'

test:
  stage: check and compile
  extends: .compile
  image: registry.cabify.services/product/guild/go/dockerfiles/docker-ci-golang-debian/docker-ci-golang-debian:latest
  dependencies: []
  before_script:
    - source /usr/local/lib/functionarium/wait_for_it.sh
    - wait_for_it mysql:3306 180
    - apt -yqq update
    - apt -yqq install default-mysql-client
    - mysql -uroot -psecret -hmysql turnero
  services:
    - name: gitlab.otters.xyz:5005/infrastructure/persistence/dockerfiles/mysql8/mysql8:latest
      command: [ "--default-authentication-plugin=mysql_native_password" ]
      alias: mysql
  variables:
    GOOS: linux
    GOARCH: amd64
    GOPROXY: "http://athens-proxy.athens-proxy.svc.cluster.local"
    GONOSUMDB: "gitlab.otters.xyz:5005/*,secondary.gitlab.otters.xyz:5005/*,gitlab.com/cabify/*,github.com/cabify/*"
    MYSQL_ROOT_PASSWORD: secret
    MYSQL_DATABASE: turnero
  script:
    - bash ./scripts/test.sh
  coverage: '/^Total test coverage: \d+.\d+\%$/'

build service image:
  stage: build
  extends: .docker
  script:
    - bash ./scripts/build.sh

security:
  extends: .docker
  stage: security
  script:
    - bash scripts/security.sh

publish:
  stage: publish
  extends: .docker
  dependencies: []
  script:
    - bash ./scripts/docker-login.sh
    - bash ./scripts/publish.sh
  artifacts:
    reports:
      dotenv: digest.env

deploy-turnero-test-manual:
  image: registry.cabify.services/platform/dev-x/deployment/kapsule-deploy:latest
  stage: deploy
  dependencies: [publish]
  variables:
    REPOSITORY: platform/dev-x/deployment/argocd-manifests
    DEPLOYMENT: services/business-automation/turnero/business-automation-1/cr.yml
    KAPSULE_DEPLOY_TOKEN: "${GITLAB_BOT_TOKEN}"
  script:
    - kapsule-deploy -r "${REPOSITORY}" -d "${DEPLOYMENT}" -t "${CONTAINER_TAG}@${DIGEST}"
  when: manual
  
deploy-turnero-test:
  image: registry.cabify.services/platform/dev-x/deployment/kapsule-deploy:latest
  stage: deploy
  dependencies: [publish]
  only:
    refs:
      - master
  variables:
    REPOSITORY: platform/dev-x/deployment/argocd-manifests
    DEPLOYMENT: services/business-automation/turnero/business-automation-1/cr.yml
    KAPSULE_DEPLOY_TOKEN: "${GITLAB_BOT_TOKEN}"
  script:
    - kapsule-deploy -r "${REPOSITORY}" -d "${DEPLOYMENT}" -t "${CONTAINER_TAG}@${DIGEST}"

deploy:
  image: registry.cabify.services/platform/dev-x/deployment/kapsule-deploy:latest
  stage: deploy
  dependencies: [publish]
  only:
    refs:
      - master
  variables:
    REPOSITORY: platform/dev-x/deployment/argocd-prod-manifests
    DEPLOYMENT: services/business-automation/turnero/production/cr.yml
    KAPSULE_DEPLOY_TOKEN: "${GITLAB_BOT_TOKEN}"
  script:
    - kapsule-deploy -r "${REPOSITORY}" -d "${DEPLOYMENT}" -t "${CONTAINER_TAG}@${DIGEST}"

