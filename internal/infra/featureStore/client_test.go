package featureStore

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"
	"turnero/internal/infra/dto"

	"gopkg.cabify.tools/servant"

	"github.com/stretchr/testify/assert"
)

func TestGetDriverLastDO(t *testing.T) {
	tests := []struct {
		name           string
		driverID       string
		serverResponse *[]dto.FeatureStoreDateTimeResponse
		serverStatus   int
		expectedTime   time.Time
		expectedError  error
	}{
		{
			name:     "successful feature retrieval",
			driverID: "123",
			serverResponse: &[]dto.FeatureStoreDateTimeResponse{
				{Value: time.Date(2024, 3, 15, 14, 30, 0, 0, time.UTC)},
			},
			serverStatus:  http.StatusOK,
			expectedTime:  time.Date(2024, 3, 15, 14, 30, 0, 0, time.UTC),
			expectedError: nil,
		},
		{
			name:           "server returns error",
			driverID:       "456",
			serverResponse: nil,
			serverStatus:   http.StatusInternalServerError,
			expectedTime:   time.Time{},
			expectedError:  fmt.Errorf("%d", http.StatusInternalServerError),
		},
		{
			name:           "driver not found",
			driverID:       "789",
			serverResponse: nil,
			serverStatus:   http.StatusNotFound,
			expectedTime:   time.Time{},
			expectedError:  ErrNotFound,
		},
		{
			name:     "invalid response format",
			driverID: "101",
			serverResponse: &[]dto.FeatureStoreDateTimeResponse{
				{Value: time.Time{}},
			},
			serverStatus:  http.StatusOK,
			expectedTime:  time.Time{},
			expectedError: ErrInvalidResponse,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request method and path
				assert.Equal(t, http.MethodGet, r.Method)
				yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
				assert.Contains(t, r.RequestURI, "/driver_last_drop_off_at?entity_id="+tt.driverID+"&date="+yesterday)

				// Set response status
				w.WriteHeader(tt.serverStatus)

				// Write response body if exists
				if tt.serverResponse != nil {
					json.NewEncoder(w).Encode(tt.serverResponse)
				}
			}))
			defer server.Close()

			// Create client with test server URL
			os.Setenv("SERVANT_HTTPCLIENT_RETRY_ENABLED", "true")
			srv := servant.NewService()

			handler := NewFeatureStoreHandler(server.URL, srv.HTTPClient())
			client := NewFeatureStoreClient(handler)

			// Execute test
			result, err := client.GetDriverLastDO(tt.driverID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedTime, result)
		})
	}
}

func TestGetDriverSegment(t *testing.T) {
	tests := []struct {
		name           string
		driverID       string
		serverResponse *[]dto.FeatureStoreStringResponse
		serverStatus   int
		expectedValue  string
		expectedError  error
	}{
		{
			name:     "successful feature retrieval",
			driverID: "123",
			serverResponse: &[]dto.FeatureStoreStringResponse{
				{Value: "segmentA"},
			},
			serverStatus:  http.StatusOK,
			expectedValue: "segmentA",
			expectedError: nil,
		},
		{
			name:           "server returns error",
			driverID:       "456",
			serverResponse: nil,
			serverStatus:   http.StatusInternalServerError,
			expectedValue:  "",
			expectedError:  fmt.Errorf("%d", http.StatusInternalServerError),
		},
		{
			name:           "driver not found",
			driverID:       "789",
			serverResponse: nil,
			serverStatus:   http.StatusNotFound,
			expectedValue:  "",
			expectedError:  ErrNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request method and path
				assert.Equal(t, http.MethodGet, r.Method)
				assert.Equal(t, "/driver_segment?entity_id="+tt.driverID, r.RequestURI)

				// Set response status
				w.WriteHeader(tt.serverStatus)

				// Write response body if exists
				if tt.serverResponse != nil {
					json.NewEncoder(w).Encode(tt.serverResponse)
				}
			}))
			defer server.Close()

			// Create client with test server URL
			os.Setenv("SERVANT_HTTPCLIENT_RETRY_ENABLED", "true")
			srv := servant.NewService()

			handler := NewFeatureStoreHandler(server.URL, srv.HTTPClient())
			client := NewFeatureStoreClient(handler)

			// Execute test
			result, err := client.GetDriverSegment(tt.driverID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedValue, result)
		})
	}
}
