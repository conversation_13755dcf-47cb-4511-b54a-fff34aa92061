// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	time "time"

	mock "github.com/stretchr/testify/mock"
)

// FeatureStoreClient is an autogenerated mock type for the FeatureStoreClient type
type FeatureStoreClient struct {
	mock.Mock
}

// GetDriverLastDO provides a mock function with given fields: driverID
func (_m *FeatureStoreClient) GetDriverLastDO(driverID string) (time.Time, error) {
	ret := _m.Called(driverID)

	if len(ret) == 0 {
		panic("no return value specified for GetDriverLastDO")
	}

	var r0 time.Time
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (time.Time, error)); ok {
		return rf(driverID)
	}
	if rf, ok := ret.Get(0).(func(string) time.Time); ok {
		r0 = rf(driverID)
	} else {
		r0 = ret.Get(0).(time.Time)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(driverID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDriverSegment provides a mock function with given fields: driverID
func (_m *FeatureStoreClient) GetDriverSegment(driverID string) (string, error) {
	ret := _m.Called(driverID)

	if len(ret) == 0 {
		panic("no return value specified for GetDriverSegment")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (string, error)); ok {
		return rf(driverID)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(driverID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(driverID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewFeatureStoreClient creates a new instance of FeatureStoreClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFeatureStoreClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *FeatureStoreClient {
	mock := &FeatureStoreClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
