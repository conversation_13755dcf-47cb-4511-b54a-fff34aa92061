package featureStore

import (
	"context"
	"fmt"
	"time"
	"turnero/internal/infra/dto"
)

const (
	FeatureDriverLastDO  = "driver_last_drop_off_at"
	FeatureRiderSegment  = "rider_segment"
	FeatureDriverSegment = "driver_segment"
)

//go:generate mockery --outpkg mocks --name FeatureStoreClient
type FeatureStoreClient interface {
	GetDriverLastDO(driverID string) (time.Time, error)
	GetDriverSegment(driverID string) (string, error)
}

type Client struct {
	handler FeatureHandler
}

func NewFeatureStoreClient(handler FeatureHandler) FeatureStoreClient {
	return &Client{handler: handler}
}

func (c *Client) GetDriverLastDO(driverID string) (time.Time, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	typeFeatureTime := &dto.FeatureStoreDateTimeResponse{}
	yesterday := time.Now().AddDate(0, 0, -1)
	response, err := c.handler.GetFeature(FeatureDriverLastDO, driverID, yesterday, ctx, typeFeatureTime)
	if err != nil {
		return time.Time{}, err
	}
	timeResponse, ok := response.(*dto.FeatureStoreDateTimeResponse)
	if !ok {
		return time.Time{}, fmt.Errorf("type assertion to *dto.FeatureStoreDateTimeResponse failed")
	}

	if timeResponse.Value.IsZero() {
		return time.Time{}, ErrInvalidResponse
	}

	return timeResponse.Value, nil
}

func (c *Client) GetDriverSegment(driverID string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	typeFeatureString := &dto.FeatureStoreStringResponse{}
	response, err := c.handler.GetFeature(FeatureDriverSegment, driverID, time.Time{}, ctx, typeFeatureString)
	if err != nil {
		return "", err
	}

	return response.(*dto.FeatureStoreStringResponse).Value, nil
}

// TODO include RiderSegment
