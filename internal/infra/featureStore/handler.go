package featureStore

import (
	"context"
	"fmt"
	"net/http"
	"time"
	"turnero/internal/infra/dto"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
)

var (
	ErrNotFound        = fmt.Errorf("not found")
	ErrInvalidResponse = fmt.Errorf("time is Zero, invalid response")
)

type FeatureHandler interface {
	GetFeature(feature, id string, date time.Time, ctx context.Context, responseType dto.FeatureStoreResponse) (dto.FeatureStoreResponse, error)
}

type FeatureStoreHandler struct {
	httpClient *http.Client
	endpoint   string
	headers    map[string]string
}

func NewFeatureStoreHandler(endpoint string, httpClient *http.Client) *FeatureStoreHandler {
	return &FeatureStoreHandler{
		endpoint: endpoint,
		headers: map[string]string{
			"Accept":           "application/json",
			"X-Cabify-Service": "turnero",
			"Content-Type":     "application/json",
		},
		httpClient: httpClient,
	}
}

func (f *FeatureStoreHandler) GetFeature(feature, id string, date time.Time, ctx context.Context, responseType dto.FeatureStoreResponse) (dto.FeatureStoreResponse, error) {
	url := f.endpoint + "/" + feature + "?entity_id=" + id
	if date != (time.Time{}) {
		url += "&date=" + date.Format(time.RFC3339)
	}
	log.Infof("Requesting feature on URL %s", url)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	for key, value := range f.headers {
		req.Header.Set(key, value)
	}
	idempotencyKey := uuid.New().String()
	req.Header.Set("X-Idempotency-Key", idempotencyKey)

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		if err := responseType.DecodeJSON(resp.Body); err != nil {
			return nil, fmt.Errorf("error decoding feature store response: %w", err)
		}
		return responseType, nil
	case http.StatusNotFound:
		return nil, ErrNotFound
	default:
		return nil, fmt.Errorf("%d", resp.StatusCode)
	}
}
