package acuity

import (
	"context"
	"errors"
	"testing"
	"turnero/internal/config"
	"turnero/internal/domain"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAcuityHandler is a mock implementation of AcuityHandler
type MockAcuityHandler struct {
	mock.Mock
}

func (m *MockAcuityHandler) GetAppointmentByID(ctx context.Context, appointmentID int) (*domain.PartnerAppointment, error) {
	args := m.Called(ctx, appointmentID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.PartnerAppointment), args.Error(1)
}

func TestClient_IsEnabled(t *testing.T) {
	tests := []struct {
		name     string
		config   *config.AcuityConfig
		expected bool
	}{
		{
			name: "disabled config",
			config: &config.AcuityConfig{
				Enabled: false,
			},
			expected: false,
		},
		{
			name: "enabled config",
			config: &config.AcuityConfig{
				Enabled: true,
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockHandler := &MockAcuityHandler{}
			client := NewClient(mockHandler, tt.config)

			result := client.IsEnabled()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestClient_GetAppointmentByID(t *testing.T) {
	tests := []struct {
		name           string
		config         *config.AcuityConfig
		appointmentID  int
		setupMocks     func(*MockAcuityHandler)
		expectedResult *domain.PartnerAppointment
		expectedError  string
	}{
		{
			name: "integration disabled",
			config: &config.AcuityConfig{
				Enabled: false,
			},
			appointmentID:  123,
			setupMocks:     func(m *MockAcuityHandler) {},
			expectedResult: nil,
			expectedError:  "acuity integration is not enabled",
		},
		{
			name: "successful retrieval",
			config: &config.AcuityConfig{
				Enabled: true,
			},
			appointmentID: 123,
			setupMocks: func(m *MockAcuityHandler) {
				appointment := &domain.PartnerAppointment{
					ID:      123,
					Partner: "acuity",
					Name:    "John",
					Surname: "Doe",
					Email:   "<EMAIL>",
				}
				m.On("GetAppointmentByID", mock.AnythingOfType("*context.timerCtx"), 123).Return(appointment, nil)
			},
			expectedResult: &domain.PartnerAppointment{
				ID:      123,
				Partner: "acuity",
				Name:    "John",
				Surname: "Doe",
				Email:   "<EMAIL>",
			},
			expectedError: "",
		},
		{
			name: "handler error",
			config: &config.AcuityConfig{
				Enabled: true,
			},
			appointmentID: 123,
			setupMocks: func(m *MockAcuityHandler) {
				m.On("GetAppointmentByID", mock.AnythingOfType("*context.timerCtx"), 123).Return(nil, errors.New("API error"))
			},
			expectedResult: nil,
			expectedError:  "API error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockHandler := &MockAcuityHandler{}
			tt.setupMocks(mockHandler)

			client := NewClient(mockHandler, tt.config)

			result, err := client.GetAppointmentByID(tt.appointmentID)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}

			mockHandler.AssertExpectations(t)
		})
	}
}
