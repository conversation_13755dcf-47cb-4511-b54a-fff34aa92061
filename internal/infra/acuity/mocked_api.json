{"uuid": "7405b586-4f76-416b-bbb0-8c4c9bafaed8", "lastMigration": 33, "name": "Mocked api", "endpointPrefix": "", "latency": 0, "port": 3002, "hostname": "", "folders": [], "routes": [{"uuid": "cdc6a244-c003-4afe-85ed-d666bd98459b", "type": "http", "documentation": "", "method": "get", "endpoint": "appointments/:id", "responses": [{"uuid": "1fc42509-953f-4ecf-be79-ffa9a90e5514", "body": "{\n  \"id\": 54321,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"McTest\",\n  \"phone\": \"\",\n  \"email\": \"<EMAIL>\",\n  \"date\": \"June 17, 2013\",\n  \"time\": \"10:15am\",\n  \"endTime\": \"11:15am\",\n  \"dateCreated\": \"July 2, 2013\",\n  \"datetime\": \"2013-06-17T10:15:00-0700\",\n  \"price\": \"10.00\",\n  \"paid\": \"no\",\n  \"amountPaid\": \"0.00\",\n  \"type\": \"Regular Visit\",\n  \"appointmentTypeID\": 1,\n  \"classID\": null,\n  \"duration\": \"60\",\n  \"calendar\": \"My Calendar\",\n  \"calendarID\": 27238,\n  \"canClientCancel\": false,\n  \"canClientReschedule\": false,\n  \"location\": \"\",\n  \"certificate\": null,\n  \"confirmationPage\": \"https://acuityscheduling.com/schedule.php?owner=11145481&id[]=1220aa9f41091c50c0cc659385cfa1d0&action=appt\",\n  \"formsText\": \"...\",\n  \"notes\": \"Notes\",\n  \"timezone\": \"America/New_York\",\n  \"scheduledBy\": null,\n  \"forms\": [\n    {\n      \"id\": 1,\n      \"name\": \"Turnero Form\",\n      \"values\": [\n        {\n          \"value\": \"añadir vehiculo\",\n          \"name\": \"Motivo de contacto\",\n          \"fieldID\": 1,\n          \"id\": 21502993\n        },\n        {\n          \"value\": \"ba test\",\n          \"name\": \"Oficina\",\n          \"fieldID\": 2,\n          \"id\": 21502994\n        }\n      ]\n    }\n  ],\n  \"labels\": [\n    {\n      \"id\": 3,\n      \"name\": \"Completed\",\n      \"color\": \"pink\"\n    }\n  ]\n}\n", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "cdc6a244-c003-4afe-85ed-d666bd98459b"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [], "callbacks": []}