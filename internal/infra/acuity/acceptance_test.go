package acuity

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"testing"
	"time"
	"turnero/internal/config"
	"turnero/internal/infra/dto"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

// TestAcuityIntegration_Acceptance is an acceptance test that calls the real Acuity API
// This test only runs when ACUITY_ACCEPTANCE_TEST=true environment variable is set
// and when all Acuity credentials are properly configured in .env
func TestAcuityIntegration_Acceptance(t *testing.T) {
	// Skip test unless explicitly enabled
	if os.Getenv("ACUITY_ACCEPTANCE_TEST") != "true" {
		t.Skip("Skipping acceptance test. Set ACUITY_ACCEPTANCE_TEST=true to run")
	}

	// Load .env file from project root (3 levels up from internal/infra/acuity/)
	envPath := filepath.Join("..", "..", "..", ".env")
	if err := godotenv.Load(envPath); err != nil {
		t.Logf("Warning: Could not load .env file from %s: %v", envPath, err)
	}

	// Load configuration from environment
	conf, err := config.GetConfig()
	if !assert.NoError(t, err, "Failed to load configuration") {
		return
	}

	// Skip if Acuity integration is not enabled
	if conf.Acuity == nil {
		t.Skip("Acuity configuration is nil. Check your .env file has all required variables")
	}

	t.Logf("Acuity Config: APIUrl=%s, UserID=%s, Enabled=%t",
		conf.Acuity.APIUrl, conf.Acuity.UserID, conf.Acuity.Enabled)

	if !conf.Acuity.Enabled {
		t.Skip("Acuity integration is not enabled. Check your .env file has all required variables")
	}

	t.Logf("Running acceptance test with Acuity API URL: %s", conf.Acuity.APIUrl)
	t.Logf("Using User ID: %s", conf.Acuity.UserID)

	// Create HTTP client with reasonable timeout for real API calls
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Create Acuity client
	acuityHandler := NewHTTPHandler(httpClient, conf.Acuity)
	acuityClient := NewClient(acuityHandler, conf.Acuity)

	t.Run("Client_IsEnabled", func(t *testing.T) {
		enabled := acuityClient.IsEnabled()
		assert.True(t, enabled, "Acuity client should be enabled with valid configuration")
	})

	t.Run("GetAppointmentByID_Real", func(t *testing.T) {
		// Try to get a non-existent appointment (ID 999999 should not exist)
		appointment, err := acuityClient.GetAppointmentByID(999999)

		// We expect this to fail, but we want to verify we get a proper error response
		assert.Error(t, err, "Should get an error for non-existent appointment")
		assert.Nil(t, appointment, "Should not return an appointment for non-existent ID")
		t.Logf("Expected error for non-existent appointment: %v", err)
	})

	t.Run("FormExtraction", func(t *testing.T) {
		// Test form extraction methods with sample data
		sampleAppointment := &dto.AcuityAppointment{
			ID: 12345,
			Forms: []dto.AcuityForm{
				{
					ID:   1,
					Name: "Booking Form",
					Values: []dto.AcuityFormValue{
						{
							FieldID: 1,
							Name:    "Stand ID",
							Value:   "1",
						},
						{
							FieldID: 2,
							Name:    "Contact Reason ID",
							Value:   "2",
						},
					},
				},
			},
		}

		standID, err := sampleAppointment.ExtractStandID()
		if assert.NoError(t, err, "Should extract stand ID from form") {
			t.Logf("Extracted Stand ID: %d", standID)
		}

		contactReasonID, err := sampleAppointment.ExtractContactReasonID()
		if assert.NoError(t, err, "Should extract contact reason ID from form") {
			t.Logf("Extracted Contact Reason ID: %d", contactReasonID)
		}

		// Test conversion to PartnerAppointment
		partnerAppointment := sampleAppointment.ToPartnerAppointment()
		assert.NotNil(t, partnerAppointment, "Should convert to PartnerAppointment")
		assert.Equal(t, "acuity", partnerAppointment.Partner, "Should set partner as acuity")
		t.Logf("Converted to PartnerAppointment: ID=%d, Partner=%s", partnerAppointment.ID, partnerAppointment.Partner)
	})

	// Optional: Test with a real appointment ID if provided
	if realAppointmentID := os.Getenv("ACUITY_TEST_APPOINTMENT_ID"); realAppointmentID != "" {
		t.Run("GetRealAppointment", func(t *testing.T) {
			// Parse the appointment ID
			var appointmentID int
			_, err := fmt.Sscanf(realAppointmentID, "%d", &appointmentID)
			if !assert.NoError(t, err, "ACUITY_TEST_APPOINTMENT_ID must be a valid integer") {
				return
			}

			appointment, err := acuityClient.GetAppointmentByID(appointmentID)

			if err != nil {
				t.Logf("Error getting real appointment %d: %v", appointmentID, err)
				return
			}

			assert.NoError(t, err, "Should be able to get real appointment")
			assert.NotNil(t, appointment, "Should return a valid appointment")

			t.Logf("Real PartnerAppointment: ID=%d, Partner=%s, Name=%s %s, Email=%s",
				appointment.ID, appointment.Partner, appointment.Name, appointment.Surname, appointment.Email)
		})
	} else {
		t.Log("To test with a real appointment, set ACUITY_TEST_APPOINTMENT_ID environment variable")
	}
}
