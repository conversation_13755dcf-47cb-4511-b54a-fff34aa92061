package acuity

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"turnero/internal/config"

	"github.com/stretchr/testify/assert"
)

func TestHTTPHandler_GetAppointmentByID_RealResponse(t *testing.T) {
	// Test with real Acuity API response format
	realAcuityResponse := `{
		"id": 54321,
		"firstName": "Bob",
		"lastName": "McTest",
		"phone": "",
		"email": "<EMAIL>",
		"date": "June 17, 2013",
		"time": "10:15am",
		"endTime": "11:15am",
		"dateCreated": "July 2, 2013",
		"datetime": "2013-06-17T10:15:00-0700",
		"price": "10.00",
		"paid": "no",
		"amountPaid": "0.00",
		"type": "Regular Visit",
		"appointmentTypeID": 1,
		"classID": null,
		"duration": "60",
		"calendar": "My Calendar",
		"calendarID": 27238,
		"canClientCancel": false,
		"canClientReschedule": false,
		"location": "",
		"certificate": null,
		"confirmationPage": "https://acuityscheduling.com/schedule.php?owner=11145481&id[]=1220aa9f41091c50c0cc659385cfa1d0&action=appt",
		"formsText": "...",
		"notes": "Notes",
		"timezone": "America/New_York",
		"scheduledBy": null,
		"forms": [
			{
				"id": 1,
				"name": "Turnero Form",
				"values": [
					{
						"value": "Technical Support",
						"name": "Motivo de contacto",
						"fieldID": 1,
						"id": 21502993
					},
					{
						"value": "Madrid Office",
						"name": "Oficina",
						"fieldID": 2,
						"id": 21502994
					}
				]
			}
		],
		"labels": [
			{
				"id": 3,
				"name": "Completed",
				"color": "pink"
			}
		]
	}`

	// Create a test server that returns the real Acuity response
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify the request
		assert.Equal(t, "/appointments/54321", r.URL.Path)
		assert.Equal(t, "GET", r.Method)

		// Check Basic Auth
		username, password, ok := r.BasicAuth()
		assert.True(t, ok)
		assert.Equal(t, "test-user", username)
		assert.Equal(t, "test-key", password)

		// Check headers
		assert.Equal(t, "application/json", r.Header.Get("Accept"))
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "Turnero/2.0 (Cabify)", r.Header.Get("User-Agent"))

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(realAcuityResponse))
	}))
	defer server.Close()

	config := &config.AcuityConfig{
		APIUrl: server.URL,
		UserID: "test-user",
		APIKey: "test-key",
	}

	httpClient := &http.Client{Timeout: 5 * time.Second}
	handler := NewHTTPHandler(httpClient, config)

	ctx := context.Background()
	result, err := handler.GetAppointmentByID(ctx, 54321)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify PartnerAppointment fields
	assert.Equal(t, 54321, result.ID)
	assert.Equal(t, "acuity", result.Partner)
	assert.Equal(t, "Bob", result.Name)
	assert.Equal(t, "McTest", result.Surname)
	assert.Equal(t, "", result.Phone) // Empty in this response
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, "Madrid Office", result.Stand)             // From "Oficina" form field
	assert.Equal(t, "Technical Support", result.ContactReason) // From "Motivo de atención" form field

	// Verify datetime parsing with timezone conversion
	expectedTime, _ := time.Parse("2006-01-02T15:04:05-0700", "2013-06-17T10:15:00-0700")
	// Convert to America/New_York timezone as specified in the response
	nyLocation, _ := time.LoadLocation("America/New_York")
	expectedTimeInNY := expectedTime.In(nyLocation)

	assert.Equal(t, expectedTimeInNY, result.CreatedAt)
	assert.Equal(t, expectedTimeInNY, result.PlannedAt)
}

func TestHTTPHandler_GetAppointmentByID_ErrorCases(t *testing.T) {
	tests := []struct {
		name           string
		serverResponse func(w http.ResponseWriter, r *http.Request)
		expectedError  string
	}{
		{
			name: "404 not found",
			serverResponse: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusNotFound)
			},
			expectedError: "acuity API returned status 404",
		},
		{
			name: "401 unauthorized",
			serverResponse: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusUnauthorized)
			},
			expectedError: "acuity API returned status 401",
		},
		{
			name: "invalid JSON response",
			serverResponse: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"invalid": json}`))
			},
			expectedError: "failed to decode response",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := httptest.NewServer(http.HandlerFunc(tt.serverResponse))
			defer server.Close()

			config := &config.AcuityConfig{
				APIUrl: server.URL,
				UserID: "test-user",
				APIKey: "test-key",
			}

			httpClient := &http.Client{Timeout: 5 * time.Second}
			handler := NewHTTPHandler(httpClient, config)

			ctx := context.Background()
			result, err := handler.GetAppointmentByID(ctx, 12345)

			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Contains(t, err.Error(), tt.expectedError)
		})
	}
}

func TestHTTPHandler_GetAppointmentByID_FormExtraction(t *testing.T) {
	// Test form extraction with different field names
	responseWithForms := `{
		"id": 12345,
		"firstName": "Test",
		"lastName": "User",
		"phone": "+1234567890",
		"email": "<EMAIL>",
		"datetime": "2024-01-15T10:00:00-0500",
		"forms": [
			{
				"id": 1,
				"name": "Booking Form",
				"values": [
					{
						"value": "Barcelona Office",
						"name": "Stand",
						"fieldID": 1,
						"id": 1001
					},
					{
						"value": "General Inquiry",
						"name": "Contact Reason",
						"fieldID": 2,
						"id": 1002
					}
				]
			}
		]
	}`

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(responseWithForms))
	}))
	defer server.Close()

	config := &config.AcuityConfig{
		APIUrl: server.URL,
		UserID: "test-user",
		APIKey: "test-key",
	}

	httpClient := &http.Client{Timeout: 5 * time.Second}
	handler := NewHTTPHandler(httpClient, config)

	ctx := context.Background()
	result, err := handler.GetAppointmentByID(ctx, 12345)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify form extraction
	assert.Equal(t, "Barcelona Office", result.Stand)
	assert.Equal(t, "General Inquiry", result.ContactReason)
	assert.Equal(t, "Test", result.Name)
	assert.Equal(t, "User", result.Surname)
	assert.Equal(t, "+1234567890", result.Phone)
	assert.Equal(t, "<EMAIL>", result.Email)
}

func TestHTTPHandler_GetAppointmentByID_TimezoneHandling(t *testing.T) {
	// Test timezone conversion with different timezones
	tests := []struct {
		name        string
		timezone    string
		datetime    string
		expectedLoc string
	}{
		{
			name:        "America/New_York timezone",
			timezone:    "America/New_York",
			datetime:    "2024-01-15T10:00:00-0500",
			expectedLoc: "America/New_York",
		},
		{
			name:        "Europe/Madrid timezone",
			timezone:    "Europe/Madrid",
			datetime:    "2024-01-15T16:00:00+0100",
			expectedLoc: "Europe/Madrid",
		},
		{
			name:        "UTC timezone",
			timezone:    "UTC",
			datetime:    "2024-01-15T15:00:00Z",
			expectedLoc: "UTC",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			responseWithTimezone := fmt.Sprintf(`{
				"id": 12345,
				"firstName": "Test",
				"lastName": "User",
				"email": "<EMAIL>",
				"datetime": "%s",
				"timezone": "%s",
				"forms": []
			}`, tt.datetime, tt.timezone)

			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(responseWithTimezone))
			}))
			defer server.Close()

			config := &config.AcuityConfig{
				APIUrl: server.URL,
				UserID: "test-user",
				APIKey: "test-key",
			}

			httpClient := &http.Client{Timeout: 5 * time.Second}
			handler := NewHTTPHandler(httpClient, config)

			ctx := context.Background()
			result, err := handler.GetAppointmentByID(ctx, 12345)

			assert.NoError(t, err)
			assert.NotNil(t, result)

			// Verify that the time is in the correct timezone
			expectedLoc, _ := time.LoadLocation(tt.expectedLoc)
			assert.Equal(t, expectedLoc.String(), result.CreatedAt.Location().String())
			assert.Equal(t, expectedLoc.String(), result.PlannedAt.Location().String())
		})
	}
}
