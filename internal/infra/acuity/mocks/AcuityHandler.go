// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"
	domain "turnero/internal/domain"

	mock "github.com/stretchr/testify/mock"
)

// AcuityHandler is an autogenerated mock type for the AcuityHandler type
type AcuityHandler struct {
	mock.Mock
}

// GetAppointmentByID provides a mock function with given fields: ctx, appointmentID
func (_m *AcuityHandler) GetAppointmentByID(ctx context.Context, appointmentID int) (*domain.PartnerAppointment, error) {
	ret := _m.Called(ctx, appointmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentByID")
	}

	var r0 *domain.PartnerAppointment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int) (*domain.PartnerAppointment, error)); ok {
		return rf(ctx, appointmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int) *domain.PartnerAppointment); ok {
		r0 = rf(ctx, appointmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.PartnerAppointment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = rf(ctx, appointmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewAcuityHandler creates a new instance of AcuityHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAcuityHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *AcuityHandler {
	mock := &AcuityHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
