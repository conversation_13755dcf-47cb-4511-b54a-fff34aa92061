// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	domain "turnero/internal/domain"

	mock "github.com/stretchr/testify/mock"
)

// AcuityClient is an autogenerated mock type for the AcuityClient type
type AcuityClient struct {
	mock.Mock
}

// GetAppointmentByID provides a mock function with given fields: appointmentID
func (_m *AcuityClient) GetAppointmentByID(appointmentID int) (*domain.PartnerAppointment, error) {
	ret := _m.Called(appointmentID)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentByID")
	}

	var r0 *domain.PartnerAppointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*domain.PartnerAppointment, error)); ok {
		return rf(appointmentID)
	}
	if rf, ok := ret.Get(0).(func(int) *domain.PartnerAppointment); ok {
		r0 = rf(appointmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.PartnerAppointment)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appointmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsEnabled provides a mock function with no fields
func (_m *AcuityClient) IsEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// NewAcuityClient creates a new instance of AcuityClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAcuityClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *AcuityClient {
	mock := &AcuityClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
