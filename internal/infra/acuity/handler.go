package acuity

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"turnero/internal/config"
	"turnero/internal/domain"
	"turnero/internal/infra/dto"

	log "github.com/sirupsen/logrus"
)

//go:generate mockery --name AcuityHandler
type AcuityHandler interface {
	GetAppointmentByID(ctx context.Context, appointmentID int) (*domain.PartnerAppointment, error)
}

// HTTPHandler implements AcuityHandler interface for HTTP operations
type HTTPHandler struct {
	httpClient *http.Client
	config     *config.AcuityConfig
}

// NewHTTPHandler creates a new HTTP handler for Acuity API
func NewHTTPHandler(httpClient *http.Client, config *config.AcuityConfig) AcuityHandler {
	return &HTTPHandler{
		httpClient: httpClient,
		config:     config,
	}
}

func (h *HTTPHandler) GetAppointmentByID(ctx context.Context, appointmentID int) (*domain.PartnerAppointment, error) {
	url := fmt.Sprintf("%s/appointments/%d", h.config.APIUrl, appointmentID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set Basic Auth
	req.SetBasicAuth(h.config.UserID, h.config.APIKey)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Turnero/2.0 (Cabify)")

	log.Infof("Making request to Acuity API: %s", url)

	resp, err := h.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("acuity API returned status %d", resp.StatusCode)
	}

	// Read the response body for debugging
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log the raw response for debugging
	log.Debugf("Raw Acuity API response: %s", string(bodyBytes))
	log.Debugf("Response length: %d bytes", len(bodyBytes))

	var acuityAppointment dto.AcuityAppointment
	if err := json.Unmarshal(bodyBytes, &acuityAppointment); err != nil {
		log.Errorf("Failed to parse JSON response: %s", string(bodyBytes))
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	log.Debugf("Successfully retrieved appointment %d from Acuity", appointmentID)

	// Convert to PartnerAppointment
	partnerAppointment := acuityAppointment.ToPartnerAppointment()
	return partnerAppointment, nil
}
