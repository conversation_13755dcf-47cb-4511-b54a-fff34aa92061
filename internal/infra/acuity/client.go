package acuity

import (
	"context"
	"fmt"
	"time"
	"turnero/internal/config"
	"turnero/internal/domain"

	log "github.com/sirupsen/logrus"
)

// AcuityClient defines the interface for Acuity operations
//
//go:generate mockery --name AcuityClient
type AcuityClient interface {
	GetAppointmentByID(appointmentID int) (*domain.PartnerAppointment, error)
	IsEnabled() bool
}

// Client implements AcuityClient interface
type Client struct {
	handler AcuityHandler
	config  *config.AcuityConfig
}

// NewClient creates a new Acuity client
func NewClient(handler AcuityHandler, config *config.AcuityConfig) AcuityClient {
	if config == nil || !config.Enabled {
		log.Warn("Acuity integration is not enabled")
	}
	log.Infof("Acuity client initialized with API URL: %s", config.APIUrl)
	return &Client{
		handler: handler,
		config:  config,
	}
}

// IsEnabled returns true if Acuity integration is enabled
func (c *Client) IsEnabled() bool {
	if c.config == nil {
		return false
	}
	return c.config != nil && c.config.Enabled
}

// GetAppointmentByID retrieves an appointment by ID from Acuity API
func (c *Client) GetAppointmentByID(appointmentID int) (*domain.PartnerAppointment, error) {
	if !c.IsEnabled() {
		return nil, fmt.Errorf("acuity integration is not enabled")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return c.handler.GetAppointmentByID(ctx, appointmentID)
}
