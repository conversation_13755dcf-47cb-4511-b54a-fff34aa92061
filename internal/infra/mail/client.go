package mail

import (
	"turnero/internal/config"
	"turnero/internal/infra/dto"

	log "github.com/sirupsen/logrus"
)

//go:generate mockery --outpkg mocks --name MailClient
type MailClient interface {
	SendMail(to string, subject string, body string) error
}

type Client struct {
	handler Mail<PERSON><PERSON>ler
	config  *config.MailgunConfig
}

func NewMailClient(handler MailHandler, cnf *config.MailgunConfig) MailClient {
	return &Client{handler: handler, config: cnf}
}

func (c *Client) SendMail(to string, subject string, body string) error {
	payload := dto.Mail{
		To:       to,
		Subject:  subject,
		Body:     body,
		Headers:  c.config.Headers,
		FromName: c.config.RequesterName,
		From:     c.config.From,
	}
	log.WithFields(log.Fields{
		"to":             payload.To,
		"subject":        payload.Subject,
		"body":           payload.Body,
		"headers":        payload.Headers,
		"from":           payload.From,
		"requesterEmail": payload.FromName,
	}).Info("Sending email")

	err := c.handler.SendEmail(payload)
	if err != nil {
		log.Errorf("Error sending email: %v", err)
		return err
	}
	return nil
}
