// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	dto "turnero/internal/infra/dto"

	mock "github.com/stretchr/testify/mock"
)

// MailHandler is an autogenerated mock type for the MailHandler type
type MailHandler struct {
	mock.Mock
}

// SendEmail provides a mock function with given fields: content
func (_m *MailHandler) SendEmail(content dto.Mail) error {
	ret := _m.Called(content)

	if len(ret) == 0 {
		panic("no return value specified for SendEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(dto.Mail) error); ok {
		r0 = rf(content)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMailHandler creates a new instance of MailHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMailHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MailHandler {
	mock := &MailHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
