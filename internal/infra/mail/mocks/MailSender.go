// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	smtp "net/smtp"

	mock "github.com/stretchr/testify/mock"
)

// MailSender is an autogenerated mock type for the MailSender type
type MailSender struct {
	mock.Mock
}

// PlainAuth provides a mock function with given fields: identity, username, password, host
func (_m *MailSender) PlainAuth(identity string, username string, password string, host string) smtp.Auth {
	ret := _m.Called(identity, username, password, host)

	if len(ret) == 0 {
		panic("no return value specified for PlainAuth")
	}

	var r0 smtp.Auth
	if rf, ok := ret.Get(0).(func(string, string, string, string) smtp.Auth); ok {
		r0 = rf(identity, username, password, host)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(smtp.Auth)
		}
	}

	return r0
}

// SendMail provides a mock function with given fields: addr, a, from, to, msg
func (_m *MailSender) SendMail(addr string, a smtp.Auth, from string, to []string, msg []byte) error {
	ret := _m.Called(addr, a, from, to, msg)

	if len(ret) == 0 {
		panic("no return value specified for SendMail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, smtp.Auth, string, []string, []byte) error); ok {
		r0 = rf(addr, a, from, to, msg)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMailSender creates a new instance of MailSender. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMailSender(t interface {
	mock.TestingT
	Cleanup(func())
}) *MailSender {
	mock := &MailSender{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
