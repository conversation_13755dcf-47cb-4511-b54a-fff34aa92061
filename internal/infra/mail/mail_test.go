package mail

import (
	"fmt"
	"net/smtp"
	"testing"
	"turnero/internal/config"
	"turnero/internal/infra/dto"
	"turnero/internal/infra/mail/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestMailGunHandler_SendEmail(t *testing.T) {
	mockSender := mocks.NewMailSender(t)

	handler := &MailGunHandler{
		sender:  mockSender,
		auth:    smtp.PlainAuth("", "<EMAIL>", "test", "smtp.test.com"),
		address: "smtp.test.com:587",
	}

	testMail := dto.Mail{
		From:    "<EMAIL>",
		To:      "<EMAIL>",
		Subject: "Test Subject",
		Body:    "Test Body",
		Headers: map[string]string{
			"Content-Type": "text/html; charset=UTF-8",
		},
	}

	// Setup expectation
	mockSender.On("SendMail",
		"smtp.test.com:587",
		mock.AnythingOfType("*smtp.plainAuth"),
		testMail.From,
		[]string{testMail.To},
		mock.MatchedBy(func(msg []byte) bool {
			msgStr := string(msg)
			return assert.Contains(t, msgStr, "Subject: Test Subject") &&
				assert.Contains(t, msgStr, "Content-Type: text/html; charset=UTF-8") &&
				assert.Contains(t, msgStr, "Test Body") &&
				assert.Contains(t, msgStr, `"Turnero" <<EMAIL>>`)
		}),
	).Return(nil)

	// Send email
	conf, err := config.GetConfig()
	assert.NoError(t, err)
	testMail.Headers = conf.Mailgun.Headers
	testMail.FromName = conf.Mailgun.RequesterName

	err = handler.SendEmail(testMail)

	// Assertions
	assert.NoError(t, err)
	mockSender.AssertExpectations(t)
}

func TestSendMail(t *testing.T) {
	tests := []struct {
		name          string
		to            string
		subject       string
		body          string
		setupMocks    func(handler *mocks.MailHandler)
		expectedError error
	}{
		{
			name:    "successful email send",
			to:      "<EMAIL>",
			subject: "Test Subject",
			body:    "Test Body",
			setupMocks: func(handler *mocks.MailHandler) {
				handler.On("SendEmail", mock.Anything).Return(nil)
			},
			expectedError: nil,
		},
		{
			name:    "error sending email",
			to:      "<EMAIL>",
			subject: "Test Subject",
			body:    "Test Body",
			setupMocks: func(handler *mocks.MailHandler) {
				handler.On("SendEmail", mock.Anything).Return(fmt.Errorf("send error"))
			},
			expectedError: fmt.Errorf("send error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handlerMock := new(mocks.MailHandler)
			tt.setupMocks(handlerMock)

			client := NewMailClient(handlerMock, &config.MailgunConfig{})
			err := client.SendMail(tt.to, tt.subject, tt.body)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			handlerMock.AssertExpectations(t)
		})
	}
}
