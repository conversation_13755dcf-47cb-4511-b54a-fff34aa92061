package mail

import (
	"net/mail"
	"net/smtp"
	"strings"
	"turnero/internal/config"
	"turnero/internal/infra/dto"
)

//go:generate mockery --outpkg mocks --name MailSender
type MailSender interface {
	SendMail(addr string, a smtp.Auth, from string, to []string, msg []byte) error
	Plain<PERSON><PERSON>(identity string, username string, password string, host string) smtp.Auth
}

//go:generate mockery --outpkg mocks --name MailHandler
type MailHandler interface {
	SendEmail(content dto.Mail) error
}

type MailGunHandler struct {
	sender  MailSender
	auth    smtp.Auth
	address string
}

type smtpMailSender struct{}

func NewSmtpMailSender() MailSender {
	return &smtpMailSender{}
}

func (s *smtpMailSender) SendMail(addr string, a smtp.Auth, from string, to []string, msg []byte) error {
	return smtp.SendMail(addr, a, from, to, msg)
}

func (s *smtpMailSender) PlainAuth(identity string, username string, password string, host string) smtp.Auth {
	return smtp.PlainAuth(identity, username, password, host)
}

func NewMailGunHandler(smtpSender MailSender, cnf *config.MailgunConfig) *MailGunHandler {
	return &MailGunHandler{
		sender:  smtpSender,
		auth:    smtpSender.PlainAuth("", cnf.Username, cnf.Password, cnf.Server),
		address: cnf.Server + ":" + cnf.Port,
	}
}

func (m *MailGunHandler) SendEmail(content dto.Mail) error {
	var message strings.Builder
	fromAddress := mail.Address{
		Name:    content.FromName,
		Address: content.From,
	}
	//Add headers
	for key, value := range content.Headers {
		message.WriteString(key + ": " + value + "\r\n")
	}
	message.WriteString("From: " + fromAddress.String() + "\r\n")
	message.WriteString("To: " + content.To + "\r\n")
	message.WriteString("Subject: " + content.Subject + "\r\n")
	message.WriteString("\r\n")
	message.WriteString(content.Body)
	// Send the email
	return m.sender.SendMail(m.address, m.auth, content.From, []string{content.To}, []byte(message.String()))
}
