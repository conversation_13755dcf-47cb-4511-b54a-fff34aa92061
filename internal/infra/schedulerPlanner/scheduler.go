package schedulerPlanner

import (
	"fmt"

	"github.com/go-co-op/gocron/v2"
	log "github.com/sirupsen/logrus"
)

type Scheduler struct {
	scheduler gocron.Scheduler
}

type JobConfig struct {
	Name string
	Cron string
	Task func()
}

func NewScheduler(ops ...gocron.SchedulerOption) (Scheduler, error) {
	sch, err := gocron.NewScheduler(ops...)
	if err != nil {
		return Scheduler{}, err
	}
	return Scheduler{
		scheduler: sch,
	}, nil
}

func (s *Scheduler) SetupScheduledJobs(jobs ...JobConfig) error {
	for _, job := range jobs {
		if job.Cron != "" {
			_, err := s.scheduler.NewJob(gocron.CronJob(job.Cron, false), gocron.NewTask(job.Task))
			if err != nil {
				return fmt.Errorf("failed to schedule job with cron %s: %v", job.Cron, err)
			}
		} else {
			log.Warnf("No cron configured for job: %v", job.Name)
		}
	}

	if len(s.scheduler.Jobs()) == 0 {
		log.Warn("No jobs scheduled")
		return nil
	}

	s.scheduler.Start()
	log.Infof("Scheduler started with %d jobs", len(s.scheduler.Jobs()))
	return nil
}

func (s *Scheduler) NumberOfJobs() int {
	return len(s.scheduler.Jobs())
}
