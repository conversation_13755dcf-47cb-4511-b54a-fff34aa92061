package schedulerPlanner

import (
	"testing"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

func TestSetupScheduledJobs(t *testing.T) {
	t.Run("SetupSchedulers returns an error when the scheduler cannot be created", func(t *testing.T) {
		job := JobConfig{
			Cron: "This is not a valid cron expression",
			Task: func() {
				log.Infof("Processing at %v", time.Now().Format("2006-01-02 15:04:05"))
			},
		}

		emptyScheduler, err := NewScheduler()
		assert.NoError(t, err)

		err = emptyScheduler.SetupScheduledJobs(job)
		assert.Error(t, err)
	})

	t.Run("SetupSchedulers returns no jobs if on crontab is empty", func(t *testing.T) {
		job := JobConfig{
			Cron: "",
			Task: func() {
				log.Infof("Processing at %v", time.Now().Format("2006-01-02 15:04:05"))
			},
		}

		emptyScheduler, err := NewScheduler()
		assert.NoError(t, err)

		err = emptyScheduler.SetupScheduledJobs(job)
		assert.NoError(t, err)
		assert.Len(t, emptyScheduler.scheduler.Jobs(), 0)
	})
}
