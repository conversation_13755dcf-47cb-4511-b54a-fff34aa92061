// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	domain "turnero/internal/domain"

	mock "github.com/stretchr/testify/mock"
)

// ZendeskClient is an autogenerated mock type for the ZendeskClient type
type ZendeskClient struct {
	mock.Mock
}

// AddTagToTicket provides a mock function with given fields: ticketID, tag
func (_m *ZendeskClient) AddTagToTicket(ticketID int, tag domain.ZendeskTag) error {
	ret := _m.Called(ticketID, tag)

	if len(ret) == 0 {
		panic("no return value specified for AddTagToTicket")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.ZendeskTag) error); ok {
		r0 = rf(ticketID, tag)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AssignTicket provides a mock function with given fields: ticketID, deskID, agentEmail
func (_m *ZendeskClient) AssignTicket(ticketID int, deskID int, agentEmail string) error {
	ret := _m.Called(ticketID, deskID, agentEmail)

	if len(ret) == 0 {
		panic("no return value specified for AssignTicket")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int, string) error); ok {
		r0 = rf(ticketID, deskID, agentEmail)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ChangeTicketStatus provides a mock function with given fields: ticketID, status
func (_m *ZendeskClient) ChangeTicketStatus(ticketID int, status domain.ZendeskTicketStatus) error {
	ret := _m.Called(ticketID, status)

	if len(ret) == 0 {
		panic("no return value specified for ChangeTicketStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.ZendeskTicketStatus) error); ok {
		r0 = rf(ticketID, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateTicket provides a mock function with given fields: attention
func (_m *ZendeskClient) CreateTicket(attention domain.AttentionRequest) (int, int, error) {
	ret := _m.Called(attention)

	if len(ret) == 0 {
		panic("no return value specified for CreateTicket")
	}

	var r0 int
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(domain.AttentionRequest) (int, int, error)); ok {
		return rf(attention)
	}
	if rf, ok := ret.Get(0).(func(domain.AttentionRequest) int); ok {
		r0 = rf(attention)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(domain.AttentionRequest) int); ok {
		r1 = rf(attention)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(domain.AttentionRequest) error); ok {
		r2 = rf(attention)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// UpdateTicket provides a mock function with given fields: _a0
func (_m *ZendeskClient) UpdateTicket(_a0 domain.ZendeskTicket) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTicket")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.ZendeskTicket) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewZendeskClient creates a new instance of ZendeskClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewZendeskClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ZendeskClient {
	mock := &ZendeskClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
