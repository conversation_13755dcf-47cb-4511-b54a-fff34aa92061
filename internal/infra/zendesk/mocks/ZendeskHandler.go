// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	models "gopkg.cabify.tools/zdgo/models"
)

// ZendeskHandler is an autogenerated mock type for the ZendeskHandler type
type ZendeskHandler struct {
	mock.Mock
}

// AddCommentIntoTicketAtZendesk provides a mock function with given fields: ticketID, comment
func (_m *ZendeskHandler) AddCommentIntoTicketAtZendesk(ticketID string, comment models.Comment) (string, error) {
	ret := _m.Called(ticketID, comment)

	if len(ret) == 0 {
		panic("no return value specified for AddCommentIntoTicketAtZendesk")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string, models.Comment) (string, error)); ok {
		return rf(ticketID, comment)
	}
	if rf, ok := ret.Get(0).(func(string, models.Comment) string); ok {
		r0 = rf(ticketID, comment)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, models.Comment) error); ok {
		r1 = rf(ticketID, comment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AddTagsToTicket provides a mock function with given fields: ticketID, tags
func (_m *ZendeskHandler) AddTagsToTicket(ticketID string, tags []string) (*models.TagsResponse, error) {
	ret := _m.Called(ticketID, tags)

	if len(ret) == 0 {
		panic("no return value specified for AddTagsToTicket")
	}

	var r0 *models.TagsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, []string) (*models.TagsResponse, error)); ok {
		return rf(ticketID, tags)
	}
	if rf, ok := ret.Get(0).(func(string, []string) *models.TagsResponse); ok {
		r0 = rf(ticketID, tags)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TagsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, []string) error); ok {
		r1 = rf(ticketID, tags)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AssignAgentByEmail provides a mock function with given fields: ticketID, agentEmail
func (_m *ZendeskHandler) AssignAgentByEmail(ticketID string, agentEmail string) (*models.Ticket, error) {
	ret := _m.Called(ticketID, agentEmail)

	if len(ret) == 0 {
		panic("no return value specified for AssignAgentByEmail")
	}

	var r0 *models.Ticket
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*models.Ticket, error)); ok {
		return rf(ticketID, agentEmail)
	}
	if rf, ok := ret.Get(0).(func(string, string) *models.Ticket); ok {
		r0 = rf(ticketID, agentEmail)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Ticket)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(ticketID, agentEmail)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ChangeTicketStatus provides a mock function with given fields: ticketID, status
func (_m *ZendeskHandler) ChangeTicketStatus(ticketID string, status string) (*models.Ticket, error) {
	ret := _m.Called(ticketID, status)

	if len(ret) == 0 {
		panic("no return value specified for ChangeTicketStatus")
	}

	var r0 *models.Ticket
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*models.Ticket, error)); ok {
		return rf(ticketID, status)
	}
	if rf, ok := ret.Get(0).(func(string, string) *models.Ticket); ok {
		r0 = rf(ticketID, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Ticket)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(ticketID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateTicket provides a mock function with given fields: ticket
func (_m *ZendeskHandler) CreateTicket(ticket models.Ticket) (*models.Ticket, error) {
	ret := _m.Called(ticket)

	if len(ret) == 0 {
		panic("no return value specified for CreateTicket")
	}

	var r0 *models.Ticket
	var r1 error
	if rf, ok := ret.Get(0).(func(models.Ticket) (*models.Ticket, error)); ok {
		return rf(ticket)
	}
	if rf, ok := ret.Get(0).(func(models.Ticket) *models.Ticket); ok {
		r0 = rf(ticket)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Ticket)
		}
	}

	if rf, ok := ret.Get(1).(func(models.Ticket) error); ok {
		r1 = rf(ticket)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateZendeskUser provides a mock function with given fields: user
func (_m *ZendeskHandler) CreateZendeskUser(user models.CreateUser) (*models.User, error) {
	ret := _m.Called(user)

	if len(ret) == 0 {
		panic("no return value specified for CreateZendeskUser")
	}

	var r0 *models.User
	var r1 error
	if rf, ok := ret.Get(0).(func(models.CreateUser) (*models.User, error)); ok {
		return rf(user)
	}
	if rf, ok := ret.Get(0).(func(models.CreateUser) *models.User); ok {
		r0 = rf(user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}

	if rf, ok := ret.Get(1).(func(models.CreateUser) error); ok {
		r1 = rf(user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserByEmail provides a mock function with given fields: email
func (_m *ZendeskHandler) GetUserByEmail(email string) (*models.User, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByEmail")
	}

	var r0 *models.User
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*models.User, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) *models.User); ok {
		r0 = rf(email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateTicket provides a mock function with given fields: ticket
func (_m *ZendeskHandler) UpdateTicket(ticket models.Ticket) (*models.Ticket, error) {
	ret := _m.Called(ticket)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTicket")
	}

	var r0 *models.Ticket
	var r1 error
	if rf, ok := ret.Get(0).(func(models.Ticket) (*models.Ticket, error)); ok {
		return rf(ticket)
	}
	if rf, ok := ret.Get(0).(func(models.Ticket) *models.Ticket); ok {
		r0 = rf(ticket)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Ticket)
		}
	}

	if rf, ok := ret.Get(1).(func(models.Ticket) error); ok {
		r1 = rf(ticket)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewZendeskHandler creates a new instance of ZendeskHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewZendeskHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *ZendeskHandler {
	mock := &ZendeskHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
