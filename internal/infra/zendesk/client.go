package zendesk

import (
	"fmt"
	"strconv"
	"strings"
	"turnero/internal/config"
	"turnero/internal/domain"

	"gopkg.cabify.tools/zdgo/models"

	log "github.com/sirupsen/logrus"
)

const offlineAttentionTag = "offline"

//go:generate mockery --name ZendeskHandler
type ZendeskHandler interface {
	AddCommentIntoTicketAtZendesk(ticketID string, comment models.Comment) (string, error)
	CreateZendeskUser(user models.CreateUser) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	CreateTicket(ticket models.Ticket) (*models.Ticket, error)
	AssignAgentByEmail(ticketID string, agentEmail string) (*models.Ticket, error)
	AddTagsToTicket(ticketID string, tags []string) (*models.TagsResponse, error)
	ChangeTicketStatus(ticketID, status string) (*models.Ticket, error)
	UpdateTicket(ticket models.Ticket) (*models.Ticket, error)
}

//go:generate mockery --name ZendeskClient
type ZendeskClient interface {
	CreateTicket(attention domain.AttentionRequest) (int, int, error)
	AssignTicket(ticketID, deskID int, agentEmail string) error
	ChangeTicketStatus(ticketID int, status domain.ZendeskTicketStatus) error
	AddTagToTicket(ticketID int, tag domain.ZendeskTag) error
	UpdateTicket(domain.ZendeskTicket) error
}

type Client struct {
	handler      ZendeskHandler
	customFields config.ZendeskCustomFields
}

func NewClient(client ZendeskHandler, customFields *config.ZendeskCustomFields) *Client {
	if customFields == nil {
		panic("customFields is required")
	}
	return &Client{client, *customFields}
}

func (c *Client) CreateTicket(attention domain.AttentionRequest) (int, int, error) {
	var zUserID int
	//Search for the zendesk User
	if attention.Requester.Email == "" {
		return 0, 0, fmt.Errorf("email is required")
	}
	zUser, err := c.handler.GetUserByEmail(attention.Requester.Email)
	if err != nil && !errNotFound(err) {
		return 0, 0, err
	}
	//If the user does not exist, create it
	if zUser == nil {
		newUser, err := c.handler.CreateZendeskUser(models.CreateUser{
			Name:  attention.Requester.Name,
			Email: attention.Requester.Email,
			Role:  "end-user",
		})
		if err != nil {
			return 0, 0, err
		}
		zUserID = int(newUser.ID)
	} else {
		zUserID = int(zUser.ID)
	}

	// Create the ticket payload
	ticket := c.generateTicketPayload(attention, zUserID)

	createdTicket, err := c.handler.CreateTicket(ticket)
	if err != nil {
		return 0, 0, err
	}

	return createdTicket.ID, 0, nil
}

func generateBodyMap(office, turn string) string {
	return fmt.Sprintf(
		"-Oficina=%s\n"+
			"-Turno=%s\n",
		office, turn)
}

func errNotFound(err error) bool {
	return strings.Contains(err.Error(), "no user found")
}

func (c *Client) generateTicketPayload(attention domain.AttentionRequest, zUserID int) models.Ticket {
	lastDO := attention.Requester.LastDOOnRequest

	// Base tags for all tickets
	tags := []string{"turnero", "offline", "stand_ID:" + joinStringWords(attention.Stand.Name, "_"), joinStringWords(attention.ContactReason.Name, "_"), attention.ContactReason.InputTag}

	// Add specific tag for Acuity appointments
	if attention.Origin == "acuity" {
		tags = append(tags, "turnero_cita_previa")
	}

	ticket := models.Ticket{
		Subject: attention.ContactReason.Name,
		Comment: &models.Comment{
			Body: generateBodyMap(attention.Stand.Name, attention.LineID),
		},
		RequesterID: zUserID,
		IsPublic:    true,
		Tags:        tags,
		CustomFields: []models.TicketCustomField{
			{
				ID:    c.customFields.Country,
				Value: getZendeskCountryName(attention.CountryName),
			},
		},
	}
	if !lastDO.IsZero() {
		ticket.Tags = append(ticket.Tags, lastDO.Format("2006-01-02"))
	} else {
		ticket.Tags = append(ticket.Tags, "NO_DROP_OFF")
	}
	log.Infof("Ticket Payload created: %+v", ticket)
	return ticket
}

func (c *Client) AssignTicket(ticketID, deskID int, agentEmail string) error {
	_, err := c.handler.AssignAgentByEmail(strconv.Itoa(ticketID), agentEmail)
	if err != nil {
		return err
	}

	_, err = c.handler.AddTagsToTicket(strconv.Itoa(ticketID), []string{"desk_ID:" + strconv.Itoa(deskID)})
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) ChangeTicketStatus(ticketID int, status domain.ZendeskTicketStatus) error {
	_, err := c.handler.ChangeTicketStatus(strconv.Itoa(ticketID), status.String())
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) AddTagToTicket(ticketID int, tag domain.ZendeskTag) error {
	_, err := c.handler.AddTagsToTicket(strconv.Itoa(ticketID), []string{tag.String()})
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) UpdateTicket(ticket domain.ZendeskTicket) error {
	payload := c.generateTicketUpdatePayload(ticket)
	_, err := c.handler.UpdateTicket(payload)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) generateTicketUpdatePayload(ticket domain.ZendeskTicket) models.Ticket {
	customFields := make([]models.TicketCustomField, 0)

	if ticket.InputTag != "" {
		customFields = append(customFields, models.TicketCustomField{
			ID:    c.customFields.InputTag,
			Value: ticket.InputTag,
		})
	}

	if ticket.OutputTag != "" {
		customFields = append(customFields, models.TicketCustomField{
			ID:    c.customFields.ContactReason, // Corresponds to 'motivo de contacto'
			Value: ticket.OutputTag,
		})
	}

	return models.Ticket{
		ID:           ticket.ID,
		CustomFields: customFields,
	}
}

func joinStringWords(str string, joinBy string) string {
	return strings.Join(strings.Fields(str), joinBy)
}

func getZendeskCountryName(countryName string) string {
	switch countryName {
	case "ESP":
		return "maxi_mobility_spain_s_l"
	case "ARG":
		return "cabify_argentina"
	case "MEX":
		return "cabify_mexico_s_de_rl_de_cv"
	case "COL":
		return "cabify_colombia_sas"
	case "CHL":
		return "maxi_mobility_chile_ii_spa"
	case "PER":
		return "maxi_mobility_peru_sac"
	case "URY":
		return "uruguay"
	default:
		log.Errorf("Country %s not found", countryName)
		return ""
	}
}
