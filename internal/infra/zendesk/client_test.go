package zendesk

import (
	"errors"
	"fmt"
	"testing"
	"time"
	"turnero/internal/config"
	"turnero/internal/domain"
	"turnero/internal/infra/zendesk/mocks"

	"gopkg.cabify.tools/zdgo/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestCreateTicket(t *testing.T) {
	tests := []struct {
		name          string
		request       domain.AttentionRequest
		lastDO        time.Time
		setupMocks    func(*mocks.ZendeskHandler)
		expectedID    int
		expectedError error
	}{
		{
			name: "successful ticket creation with a existing user",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:        1,
					Name:      "Technical Support",
					InputTag:  "tech_support",
					OutputTag: "resolved",
				},
				Stand: domain.Stand{
					Name: "Main Office",
				},
				Requester: domain.Requester{
					Email:   "<EMAIL>",
					Name:    "<PERSON>",
					Surname: "<PERSON><PERSON>",
				},
			},
			//lastDO: time.Now(),
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("GetUserByEmail", mock.Anything).Return(&models.User{Name: "John", Email: "<EMAIL>", ID: 1234}, nil)
				handler.On("CreateTicket", mock.Anything).Return(&models.Ticket{ID: 123456}, nil)
			},
			expectedID:    123456,
			expectedError: nil,
		},
		{
			name: "successful ticket creation with a new user",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:        1,
					Name:      "Technical Support",
					InputTag:  "tech_support",
					OutputTag: "resolved",
				},
				Stand: domain.Stand{
					Name: "Main Office",
				},
				Requester: domain.Requester{
					Email:   "<EMAIL>",
					Name:    "John",
					Surname: "Doe",
				},
			},
			lastDO: time.Now(),
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("GetUserByEmail", mock.Anything).Return(nil, nil)
				handler.On("CreateZendeskUser", mock.Anything).Return(&models.User{ID: 1234}, nil)
				handler.On("CreateTicket", mock.Anything).Return(&models.Ticket{ID: 123456}, nil)
			},
			expectedID:    123456,
			expectedError: nil,
		},
		{
			name: "missing customer information",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:   1,
					Name: "Technical Support",
				},
				Stand: domain.Stand{
					Name: "Main Office",
				},
			},
			lastDO:        time.Now(),
			setupMocks:    func(handler *mocks.ZendeskHandler) {},
			expectedID:    0,
			expectedError: fmt.Errorf("email is required"),
		},
		{
			name: "zendesk API error",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:        1,
					Name:      "Technical Support",
					InputTag:  "tech_support",
					OutputTag: "resolved",
				},
				Stand: domain.Stand{
					Name: "Main Office",
				},
				Requester: domain.Requester{
					Email:   "<EMAIL>",
					Name:    "John",
					Surname: "Doe",
				},
			},
			lastDO: time.Now(),
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("GetUserByEmail", mock.Anything).Return(&models.User{Name: "John", Email: "<EMAIL>", ID: 1234}, nil)
				handler.On("CreateTicket", mock.Anything).Return(&models.Ticket{}, fmt.Errorf("API error"))
			},
			expectedID:    0,
			expectedError: fmt.Errorf("API error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock handler
			mockHandler := new(mocks.ZendeskHandler)
			tt.setupMocks(mockHandler)

			// Create client instance
			client := NewClient(mockHandler, &config.ZendeskCustomFields{})

			// Execute test
			ticketID, _, err := client.CreateTicket(tt.request)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedID, ticketID)

			// Verify all expectations were met
			mockHandler.AssertExpectations(t)
		})
	}
}

func TestGenerateTicketPayload(t *testing.T) {
	validComment := &models.Comment{
		Body: generateBodyMap("Main Office", ""),
	}
	tests := []struct {
		name     string
		request  domain.AttentionRequest
		zUserID  int
		expected models.Ticket
	}{
		{
			name: "with lastDO",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:        1,
					Name:      "Technical Support",
					InputTag:  "tech_support",
					OutputTag: "resolved",
				},
				Stand: domain.Stand{
					Name: "Main Office",
					ID:   1,
				},
				Requester: domain.Requester{
					Email:           "<EMAIL>",
					LastDOOnRequest: time.Now(),
				},
				CountryName: "ESP",
			},
			zUserID: 1234,
			expected: models.Ticket{
				Subject:     "Technical Support",
				Comment:     validComment,
				RequesterID: 1234,
				IsPublic:    true,
				Tags:        []string{"turnero", "offline", "stand_ID:Main_Office", "Technical_Support", time.Now().Format("2006-01-02"), "tech_support"},
			},
		},
		{
			name: "without lastDO",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:        1,
					Name:      "Technical Support",
					InputTag:  "tech_support",
					OutputTag: "resolved",
				},
				Stand: domain.Stand{
					Name: "Main Office",
					ID:   1,
				},
				Requester: domain.Requester{
					Email: "<EMAIL>",
				},
				CountryName: "ESP",
			},
			zUserID: 1234,
			expected: models.Ticket{
				Subject:     "Technical Support",
				Comment:     validComment,
				RequesterID: 1234,
				IsPublic:    true,
				Tags:        []string{"turnero", "offline", "stand_ID:Main_Office", "Technical_Support", "NO_DROP_OFF", "tech_support"},
			},
		},
		{
			name: "Acuity appointment with turnero_cita_previa tag",
			request: domain.AttentionRequest{
				ContactReason: domain.ContactReason{
					ID:        1,
					Name:      "Technical Support",
					InputTag:  "tech_support",
					OutputTag: "resolved",
				},
				Stand: domain.Stand{
					Name: "Main Office",
					ID:   1,
				},
				Requester: domain.Requester{
					Email: "<EMAIL>",
				},
				CountryName: "ESP",
				Origin:      "acuity",
			},
			zUserID: 1234,
			expected: models.Ticket{
				Subject:     "Technical Support",
				Comment:     validComment,
				RequesterID: 1234,
				IsPublic:    true,
				Tags:        []string{"turnero", "offline", "stand_ID:Main_Office", "Technical_Support", "turnero_cita_previa", "NO_DROP_OFF", "tech_support"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &Client{}
			ticket := client.generateTicketPayload(tt.request, tt.zUserID)

			assert.Equal(t, tt.expected.Subject, ticket.Subject)
			assert.Equal(t, tt.expected.Comment.Body, ticket.Comment.Body)
			assert.Equal(t, tt.expected.RequesterID, ticket.RequesterID)
			assert.Equal(t, tt.expected.IsPublic, ticket.IsPublic)
			assert.ElementsMatch(t, tt.expected.Tags, ticket.Tags)
		})
	}
}

func TestChangeTicketStatus(t *testing.T) {
	tests := []struct {
		name          string
		ticketID      int
		status        domain.ZendeskTicketStatus
		setupMocks    func(*mocks.ZendeskHandler)
		expectedError error
	}{
		{
			name:     "successful status change",
			ticketID: 123,
			status:   domain.ZendeskTicketStatus("open"),
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("ChangeTicketStatus", "123", "open").Return(nil, nil)
			},
			expectedError: nil,
		},
		{
			name:     "error changing status",
			ticketID: 123,
			status:   domain.ZendeskTicketStatus("open"),
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("ChangeTicketStatus", "123", "open").Return(nil, errors.New("API error"))
			},
			expectedError: errors.New("API error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockHandler := new(mocks.ZendeskHandler)
			tt.setupMocks(mockHandler)

			client := NewClient(mockHandler, &config.ZendeskCustomFields{})
			err := client.ChangeTicketStatus(tt.ticketID, tt.status)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			mockHandler.AssertExpectations(t)
		})
	}
}

func TestAddTagToTicket(t *testing.T) {
	tests := []struct {
		name          string
		ticketID      int
		tag           domain.ZendeskTag
		setupMocks    func(*mocks.ZendeskHandler)
		expectedError error
	}{
		{
			name:     "successful tag addition",
			ticketID: 123,
			tag:      "urgent",
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("AddTagsToTicket", "123", []string{"urgent"}).Return(nil, nil)
			},
			expectedError: nil,
		},
		{
			name:     "error adding tag",
			ticketID: 123,
			tag:      "urgent",
			setupMocks: func(handler *mocks.ZendeskHandler) {
				handler.On("AddTagsToTicket", "123", []string{"urgent"}).Return(nil, errors.New("API error"))
			},
			expectedError: errors.New("API error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockHandler := new(mocks.ZendeskHandler)
			tt.setupMocks(mockHandler)

			client := NewClient(mockHandler, &config.ZendeskCustomFields{})
			err := client.AddTagToTicket(tt.ticketID, tt.tag)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			mockHandler.AssertExpectations(t)
		})
	}
}

func TestGenerateTicketUpdatePayload(t *testing.T) {
	tests := []struct {
		name     string
		ticket   domain.ZendeskTicket
		expected models.Ticket
	}{
		{
			name: "with input and output tags",
			ticket: domain.ZendeskTicket{
				ID:        123,
				InputTag:  "input_tag_value",
				OutputTag: "output_tag_value",
			},
			expected: models.Ticket{
				ID: 123,
				CustomFields: []models.TicketCustomField{
					{
						ID:    1, // Assuming 1 is the ID for InputTag in customFields
						Value: "input_tag_value",
					},
					{
						ID:    2, // Assuming 2 is the ID for ContactReason in customFields
						Value: "output_tag_value",
					},
				},
			},
		},
		{
			name: "with only input tag",
			ticket: domain.ZendeskTicket{
				ID:       123,
				InputTag: "input_tag_value",
			},
			expected: models.Ticket{
				ID: 123,
				CustomFields: []models.TicketCustomField{
					{
						ID:    1, // Assuming 1 is the ID for InputTag in customFields
						Value: "input_tag_value",
					},
				},
			},
		},
		{
			name: "with only output tag",
			ticket: domain.ZendeskTicket{
				ID:        123,
				OutputTag: "output_tag_value",
			},
			expected: models.Ticket{
				ID: 123,
				CustomFields: []models.TicketCustomField{
					{
						ID:    2, // Assuming 2 is the ID for ContactReason in customFields
						Value: "output_tag_value",
					},
				},
			},
		},
		{
			name: "without tags",
			ticket: domain.ZendeskTicket{
				ID: 123,
			},
			expected: models.Ticket{
				ID:           123,
				CustomFields: []models.TicketCustomField{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &Client{
				customFields: config.ZendeskCustomFields{
					InputTag:      1,
					ContactReason: 2,
				},
			}
			result := client.generateTicketUpdatePayload(tt.ticket)
			assert.Equal(t, tt.expected, result)
		})
	}
}
