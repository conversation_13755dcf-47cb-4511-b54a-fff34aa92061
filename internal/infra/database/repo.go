package database

import (
	"database/sql"
	"turnero/internal/infra/dto"

	"github.com/jmoiron/sqlx"
)

//go:generate mockery --outpkg mocks --name Repo
type Repo interface {
	GetAgentByEmail(email string) (dto.Agent, error)
	GetAgentByID(id int) (dto.Agent, error)
	GetAllAgents() ([]dto.Agent, error)
	CreateAgent(agent dto.Agent) (int, error)
	UpdateAgent(id int, agent dto.Agent) error
	DeleteAgent(id int) error
	GetWorkingAgentsByStandID(standID int) ([]dto.Agent, error)
	SetAgentIsWorking(agentID int, isWorking bool) error
	SetAgentDesk(agentID int, desk int) error
	GetStandByID(standID int) (dto.Stand, error)
	GetStandByName(name string) (dto.Stand, error)
	CreateStand(stand dto.Stand) (int, error)
	UpdateStand(id int, stand dto.Stand) error
	DeleteStand(id int) error
	GetStandDayAppointments(standID int, timezone string) ([]dto.Appointment, error)
	GetStandCurrentMonthAppointmentsCount(standID int) (int, error)
	GetStandSelectedDesksByItsAgents(standID int) ([]int, error)
	GetCountryByID(id int) (dto.Country, error)
	GetAllCountries() ([]dto.Country, error)
	GetContactReasonsByCountry(countryID int) ([]dto.ContactReason, error)
	GetContactReasonsByStand(standID int) ([]dto.ContactReason, error)
	GetContactReasonIDsByStand(standID int) ([]int, error)
	SetStandContactReasons(standID int, contactReasonIDs []int) error
	GetContactReasonByID(id int) (dto.ContactReason, error)
	GetContactReasonByName(name string) (dto.ContactReason, error)
	GetAllContactReasons() ([]dto.ContactReason, error)
	CreateContactReason(contactReason dto.ContactReason) (int, error)
	UpdateContactReason(id int, contactReason dto.ContactReason) error
	DeleteContactReason(id int) error
	CreateRequester(requester dto.Requester) error
	GetRequesterByEmail(email string) (dto.Requester, error)
	GetRequesterByID(id int) (dto.Requester, error)
	SaveAppointment(appointment dto.Appointment) error
	GetAppointmentByID(id int) (dto.Appointment, error)
	GetAppointmentByZendeskID(id int) (dto.Appointment, error)
	GetAppointmentByPartnerID(partnerID int) (dto.Appointment, error)
	CountryRelatedToStandID(id int) (dto.Country, error)
	CloseAllWorkingTurnsAndUnsetDesks() error
	GetAllStands() ([]dto.Stand, error)
}

type MysqlRepository struct {
	db *sqlx.DB
}

func NewRepository(db *sqlx.DB) *MysqlRepository {
	return &MysqlRepository{db}
}

func (m MysqlRepository) GetAgentByEmail(email string) (dto.Agent, error) {
	var agent dto.Agent
	err := m.db.Get(&agent, "SELECT id, email, stand_id, is_working, desk, role FROM agents WHERE email = ? AND is_deleted = FALSE", email)
	if err != nil {
		return dto.Agent{}, err
	}
	return agent, nil
}

func (m MysqlRepository) GetAgentByID(id int) (dto.Agent, error) {
	var agent dto.Agent
	err := m.db.Get(&agent, "SELECT id, email, stand_id, is_working, desk, role FROM agents WHERE id = ? AND is_deleted = FALSE", id)
	if err != nil {
		return dto.Agent{}, err
	}
	return agent, nil
}

func (m MysqlRepository) GetAllAgents() ([]dto.Agent, error) {
	var agents []dto.Agent
	err := m.db.Select(&agents, "SELECT id, email, stand_id, is_working, desk, role FROM agents WHERE is_deleted = FALSE ORDER BY email")
	if err != nil {
		return nil, err
	}
	return agents, nil
}

func (m MysqlRepository) CreateAgent(agent dto.Agent) (int, error) {
	query := `
		INSERT INTO agents (email, stand_id, is_working, desk, role)
		VALUES (?, ?, ?, ?, ?)
	`
	result, err := m.db.Exec(query, agent.Email, agent.StandID, agent.IsWorking, agent.Desk, agent.Role)
	if err != nil {
		return 0, err
	}
	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}
	return int(id), nil
}

func (m MysqlRepository) UpdateAgent(id int, agent dto.Agent) error {
	query := `
		UPDATE agents
		SET email = ?, stand_id = ?, is_working = ?, desk = ?, role = ?
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, agent.Email, agent.StandID, agent.IsWorking, agent.Desk, agent.Role, id)
	return err
}

func (m MysqlRepository) DeleteAgent(id int) error {
	query := `
		UPDATE agents
		SET is_deleted = TRUE
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, id)
	return err
}

func (m MysqlRepository) GetStandByID(standID int) (dto.Stand, error) {
	var stand dto.Stand
	err := m.db.Get(&stand, "SELECT id, name, country_id FROM stands WHERE id = ? AND is_deleted = FALSE", standID)
	if err != nil {
		return dto.Stand{}, err
	}
	return stand, nil
}

func (m MysqlRepository) GetContactReasonsByCountry(countryID int) ([]dto.ContactReason, error) {
	var contactReasons []dto.ContactReason
	query := `
		SELECT cr.id, cr.name, cr.audience, cr.kind, cr.detail, cr.input_tag, cr.output_tag, cr.solution
		FROM contact_reasons cr
		JOIN contact_reasons_countries crc ON cr.id = crc.contact_reason_id
		WHERE crc.country_id = ? AND cr.is_deleted = FALSE AND crc.is_deleted = FALSE
	`
	err := m.db.Select(&contactReasons, query, countryID)
	if err != nil {
		return nil, err
	}
	return contactReasons, nil
}

func (m MysqlRepository) GetContactReasonsByStand(standID int) ([]dto.ContactReason, error) {
	var contactReasons []dto.ContactReason
	query := `
		SELECT cr.id, cr.name, cr.audience, cr.kind, cr.detail, cr.input_tag, cr.output_tag, cr.solution, cr.category
		FROM contact_reasons cr
		JOIN contact_reasons_stands crs ON cr.id = crs.contact_reason_id
		WHERE crs.stand_id = ? AND cr.is_deleted = FALSE AND crs.is_deleted = FALSE
		ORDER BY cr.name
	`
	err := m.db.Select(&contactReasons, query, standID)
	if err != nil {
		return nil, err
	}
	return contactReasons, nil
}

func (m MysqlRepository) GetAllContactReasons() ([]dto.ContactReason, error) {
	var contactReasons []dto.ContactReason
	err := m.db.Select(&contactReasons, "SELECT id, name, audience, kind, detail, input_tag, output_tag, solution, category FROM contact_reasons WHERE is_deleted = FALSE ORDER BY name")
	if err != nil {
		return nil, err
	}
	return contactReasons, nil
}

func (m MysqlRepository) GetContactReasonByID(id int) (dto.ContactReason, error) {
	var contactReason dto.ContactReason
	err := m.db.Get(&contactReason, "SELECT id, name, audience, kind, detail, input_tag, output_tag, solution, category FROM contact_reasons WHERE id = ? AND is_deleted = FALSE", id)
	if err != nil {
		return dto.ContactReason{}, err
	}
	return contactReason, nil
}

func (m MysqlRepository) GetContactReasonByName(name string) (dto.ContactReason, error) {
	var contactReason dto.ContactReason
	err := m.db.Get(&contactReason, "SELECT id, name, audience, kind, detail, input_tag, output_tag, solution, category FROM contact_reasons WHERE name = ? AND is_deleted = FALSE", name)
	if err != nil {
		return dto.ContactReason{}, err
	}
	return contactReason, nil
}

func (m MysqlRepository) CreateContactReason(contactReason dto.ContactReason) (int, error) {
	query := `
		INSERT INTO contact_reasons (name, audience, kind, detail, input_tag, output_tag, solution, category)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`
	result, err := m.db.Exec(query, contactReason.Name, contactReason.Audience, contactReason.Kind,
		contactReason.Detail, contactReason.InputTag, contactReason.OutputTag, contactReason.Solution, contactReason.Category)
	if err != nil {
		return 0, err
	}
	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}
	return int(id), nil
}

func (m MysqlRepository) UpdateContactReason(id int, contactReason dto.ContactReason) error {
	query := `
		UPDATE contact_reasons
		SET name = ?, audience = ?, kind = ?, detail = ?, input_tag = ?, output_tag = ?, solution = ?, category = ?
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, contactReason.Name, contactReason.Audience, contactReason.Kind,
		contactReason.Detail, contactReason.InputTag, contactReason.OutputTag, contactReason.Solution, contactReason.Category, id)
	return err
}

func (m MysqlRepository) DeleteContactReason(id int) error {
	query := `
		UPDATE contact_reasons
		SET is_deleted = TRUE
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, id)
	return err
}

func (m MysqlRepository) GetStandByName(name string) (dto.Stand, error) {
	var stand dto.Stand
	err := m.db.Get(&stand, "SELECT id, name, country_id FROM stands WHERE name = ? AND is_deleted = FALSE", name)
	if err != nil {
		return dto.Stand{}, err
	}
	return stand, nil
}

func (m MysqlRepository) CreateStand(stand dto.Stand) (int, error) {
	query := `
		INSERT INTO stands (name, country_id)
		VALUES (?, ?)
	`
	result, err := m.db.Exec(query, stand.Name, stand.CountryID)
	if err != nil {
		return 0, err
	}
	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}
	return int(id), nil
}

func (m MysqlRepository) UpdateStand(id int, stand dto.Stand) error {
	query := `
		UPDATE stands
		SET name = ?, country_id = ?
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, stand.Name, stand.CountryID, id)
	return err
}

func (m MysqlRepository) DeleteStand(id int) error {
	query := `
		UPDATE stands
		SET is_deleted = TRUE
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, id)
	return err
}

func (m MysqlRepository) GetRequesterByEmail(email string) (dto.Requester, error) {
	var requester dto.Requester
	err := m.db.Get(&requester, "SELECT id, user_id, name, surname, email, segment FROM requesters WHERE email = ?", email)
	if err != nil {
		return dto.Requester{}, err
	}
	return requester, nil
}

func (m MysqlRepository) CreateRequester(requester dto.Requester) error {
	query := `
		INSERT INTO requesters (user_id, name, surname, email, segment)
		VALUES (?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			user_id = VALUES(user_id),
			name = VALUES(name),
			surname = VALUES(surname),
			email = VALUES(email),
			segment = VALUES(segment)
	`
	_, err := m.db.Exec(query, requester.UserID, requester.Name, requester.Surname, requester.Email, requester.Segment)
	if err != nil {
		return err
	}
	return nil
}

func (m MysqlRepository) SaveAppointment(appointment dto.Appointment) error {
	query := `
		INSERT INTO appointments (id, requester_id, line_id, contact_reason, zticket_id, comments, stand_id, agent_id, status, last_do, attended_at, planned_at, origin, partner_id)
		VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			line_id = VALUES(line_id),
			contact_reason = VALUES(contact_reason),
			zticket_id = VALUES(zticket_id),
			comments = VALUES(comments),
			stand_id = VALUES(stand_id),
			agent_id = VALUES(agent_id),
			status = VALUES(status),
			last_do = VALUES(last_do),
			attended_at = VALUES(attended_at),
			planned_at = VALUES(planned_at),
			origin = VALUES(origin),
			partner_id = VALUES(partner_id)
	`
	_, err := m.db.Exec(query, appointment.ID, appointment.RequesterID, appointment.LineID, appointment.ContactReason, appointment.ZTicketID, appointment.Comments, appointment.StandID, appointment.AgentID, appointment.Status, appointment.LastDo, appointment.AttendedAt, appointment.PlannedAt, appointment.Origin, appointment.PartnerID)
	if err != nil {
		return err
	}
	return nil
}

func (m MysqlRepository) GetAppointmentByID(id int) (dto.Appointment, error) {
	var appointment dto.Appointment
	query := `
		SELECT id, requester_id, line_id, contact_reason, zticket_id, comments, stand_id, agent_id, status, last_do, attended_at, planned_at, origin, partner_id
		FROM appointments
		WHERE id = ?
	`
	err := m.db.Get(&appointment, query, id)
	if err != nil {
		return dto.Appointment{}, err
	}
	return appointment, nil
}

func (m MysqlRepository) GetCountryByID(id int) (dto.Country, error) {
	var country dto.Country
	query := `
  SELECT id, name
  FROM countries
  WHERE id = ? AND is_deleted = FALSE
 `
	err := m.db.Get(&country, query, id)
	if err != nil {
		return dto.Country{}, err
	}
	return country, nil
}

func (m MysqlRepository) GetAllCountries() ([]dto.Country, error) {
	var countries []dto.Country
	query := `
  SELECT id, name
  FROM countries
  WHERE is_deleted = FALSE
  ORDER BY name
 `
	err := m.db.Select(&countries, query)
	if err != nil {
		return nil, err
	}
	return countries, nil
}

func (m MysqlRepository) GetStandCurrentMonthAppointmentsCount(standID int) (int, error) {
	var count int
	query := `
  SELECT COUNT(*)
  FROM appointments
  WHERE stand_id = ? AND YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())
 `
	err := m.db.Get(&count, query, standID)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m MysqlRepository) GetWorkingAgentsByStandID(standID int) ([]dto.Agent, error) {
	var agents []dto.Agent
	query := `
		SELECT id, email, stand_id, is_working, desk, role
		FROM agents
		WHERE stand_id = ? AND is_working = TRUE AND is_deleted = FALSE
	`
	err := m.db.Select(&agents, query, standID)
	if err != nil {
		return nil, err
	}
	return agents, nil
}

// GetStandDayAppointments returns all appointments for a stand in the current day ordered by planned date
func (m MysqlRepository) GetStandDayAppointments(standID int, timezone string) ([]dto.Appointment, error) {
	var appointments []dto.Appointment
	query := `
  SELECT id, requester_id, line_id, contact_reason, zticket_id, comments, stand_id, agent_id, status, last_do, created_at, planned_at, origin, partner_id
  FROM appointments
  WHERE stand_id = ? AND DATE(CONVERT_TZ(planned_at, 'UTC', ?)) = CURDATE()
  ORDER BY planned_at
 `
	err := m.db.Select(&appointments, query, standID, timezone)
	if err != nil {
		return nil, err
	}
	return appointments, nil
}

func (m MysqlRepository) SetAgentIsWorking(agentID int, isWorking bool) error {
	query := `
		UPDATE agents
		SET is_working = ?
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, isWorking, agentID)
	if err != nil {
		return err
	}
	return nil
}

func (m MysqlRepository) GetRequesterByID(id int) (dto.Requester, error) {
	var requester dto.Requester
	err := m.db.Get(&requester, "SELECT id, user_id, name, surname, email, segment FROM requesters WHERE id = ?", id)
	if err != nil {
		return dto.Requester{}, err
	}
	return requester, nil
}

func (m MysqlRepository) SetAgentDesk(agentID int, desk int) error {
	query := `
		UPDATE agents
		SET desk = ?
		WHERE id = ? AND is_deleted = FALSE
	`
	_, err := m.db.Exec(query, desk, agentID)
	if err != nil {
		return err
	}
	return nil
}

func (m MysqlRepository) GetStandSelectedDesksByItsAgents(standID int) ([]int, error) {
	var rawDesks []sql.NullInt64
	query := `
		SELECT desk
		FROM agents
		WHERE stand_id = ? AND is_deleted = FALSE
	`
	err := m.db.Select(&rawDesks, query, standID)
	if err != nil {
		return nil, err
	}

	// Filter out NULL values and convert to int
	var desks []int
	for _, rawDesk := range rawDesks {
		if rawDesk.Valid && rawDesk.Int64 > 0 {
			desks = append(desks, int(rawDesk.Int64))
		}
	}

	return desks, nil
}

func (m MysqlRepository) GetAppointmentByZendeskID(id int) (dto.Appointment, error) {
	var appointment dto.Appointment
	query := `
		SELECT id, requester_id, line_id, contact_reason, zticket_id, comments, stand_id, agent_id, status, last_do, attended_at, planned_at, origin, partner_id
		FROM appointments
		WHERE zticket_id = ?
	`
	err := m.db.Get(&appointment, query, id)
	if err != nil {
		return dto.Appointment{}, err
	}
	return appointment, nil
}

func (m MysqlRepository) GetAppointmentByPartnerID(partnerID int) (dto.Appointment, error) {
	var appointment dto.Appointment
	query := `
		SELECT id, requester_id, line_id, contact_reason, zticket_id, comments, stand_id, agent_id, status, last_do, attended_at, planned_at, origin, partner_id
		FROM appointments
		WHERE partner_id = ? AND origin = 'acuity'
	`
	err := m.db.Get(&appointment, query, partnerID)
	if err != nil {
		return dto.Appointment{}, err
	}
	return appointment, nil
}

func (m MysqlRepository) CountryRelatedToStandID(id int) (dto.Country, error) {
	var country dto.Country
	query := `
		SELECT c.id, c.name
		FROM countries c
		JOIN stands s ON c.id = s.country_id
		WHERE s.id = ?
	`
	err := m.db.Get(&country, query, id)
	if err != nil {
		return dto.Country{}, err
	}
	return country, nil
}

func (m MysqlRepository) CloseAllWorkingTurnsAndUnsetDesks() error {
	query := `
		UPDATE agents
		SET is_working = FALSE, desk = 0
		WHERE is_deleted = FALSE
	`
	_, err := m.db.Exec(query)
	if err != nil {
		return err
	}
	return nil
}

func (m MysqlRepository) GetAllStands() ([]dto.Stand, error) {
	var stands []dto.Stand
	query := `
		SELECT id, name, country_id
		FROM stands
		WHERE is_deleted = FALSE
	`
	err := m.db.Select(&stands, query)
	if err != nil {
		return nil, err
	}
	return stands, nil
}

func (m MysqlRepository) GetContactReasonIDsByStand(standID int) ([]int, error) {
	var contactReasonIDs []int
	query := `
		SELECT contact_reason_id
		FROM contact_reasons_stands
		WHERE stand_id = ? AND is_deleted = FALSE
		ORDER BY contact_reason_id
	`
	err := m.db.Select(&contactReasonIDs, query, standID)
	if err != nil {
		return nil, err
	}
	return contactReasonIDs, nil
}

func (m MysqlRepository) SetStandContactReasons(standID int, contactReasonIDs []int) error {
	// Start transaction
	tx, err := m.db.Beginx()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// First, mark all existing relationships as deleted for this stand
	_, err = tx.Exec(`
		UPDATE contact_reasons_stands
		SET is_deleted = TRUE
		WHERE stand_id = ?`, standID)
	if err != nil {
		return err
	}

	// Then insert/update the new relationships
	for _, contactReasonID := range contactReasonIDs {
		_, err = tx.Exec(`
			INSERT INTO contact_reasons_stands (contact_reason_id, stand_id, is_deleted)
			VALUES (?, ?, FALSE)
			ON DUPLICATE KEY UPDATE is_deleted = FALSE`, contactReasonID, standID)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}
