package database

import (
	"encoding/json"
	"testing"
	"turnero/internal/infra/dto"

	"github.com/stretchr/testify/assert"
)

func TestInsertDataFromJSON_ContactReasonsStands(t *testing.T) {
	// Test JSON data with contact_reasons_stands
	jsonData := `{
		"countries": [],
		"contact_reasons": [],
		"stands": [],
		"agents": [],
		"contact_reasons_countries": [],
		"contact_reasons_stands": [
			{"contact_reason_id": 1, "stand_id": 1}
		]
	}`

	// Parse the JSON to verify structure
	var data dto.JSONDBData
	err := json.Unmarshal([]byte(jsonData), &data)

	// Assert no error occurred during parsing
	assert.NoError(t, err)

	// Verify contact_reasons_stands was parsed correctly
	assert.Len(t, data.ContactReasonsStands, 1)
	assert.Equal(t, 1, data.ContactReasonsStands[0].ContactReasonID)
	assert.Equal(t, 1, data.ContactReasonsStands[0].StandID)
}

func TestInsertDataFromJSON_EmptyContactReasonsStands(t *testing.T) {
	// Test JSON data without contact_reasons_stands
	jsonData := `{
		"countries": [],
		"contact_reasons": [],
		"stands": [],
		"agents": [],
		"contact_reasons_countries": []
	}`

	// Parse the JSON to verify structure
	var data dto.JSONDBData
	err := json.Unmarshal([]byte(jsonData), &data)

	// Assert no error occurred during parsing
	assert.NoError(t, err)

	// Verify contact_reasons_stands is empty
	assert.Len(t, data.ContactReasonsStands, 0)
}
