// Ignoring test from normal test run and running only in integration test run. Run with `go test -tags=integration ./...`:
//go:build integration
// +build integration

package database

import (
	"database/sql"
	"fmt"
	"net"
	"os"
	"testing"
	"time"
	"turnero/internal/config"
	"turnero/internal/domain"
	"turnero/internal/infra/dto"

	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

func TestTurneroMYSQL(t *testing.T) {
	dbClient := setupTestingDatabase(t)
	assert.NotNil(t, dbClient)

	t.Run("TestGetTabletDataByAgentEmail", func(t *testing.T) {
		agentEmail := "<EMAIL>"
		tabletData, err := dbClient.GetTabletDataByAgentEmail(agentEmail)
		assert.NoError(t, err)
		assert.NotNil(t, tabletData)

		expected := domain.TabletData{Stand: "Main Office",
			ContactReasons: []domain.ContactReason{
				domain.ContactReason{ID: 3, Name: "Feedback", Audience: "Customers", Kind: "Standard", Detail: "Provide feedback", InputTag: "feedback", Category: "", OutputTag: "resolved", Solution: "Thank the customer for their feedback"},
				domain.ContactReason{ID: 2, Name: "Sales Inquiry", Audience: "Potential Customers", Kind: "Acquisition", Detail: "Questions about products", InputTag: "sales_inquiry", Category: "", OutputTag: "follow_up", Solution: "Provide product information"},
				domain.ContactReason{ID: 1, Name: "Technical Support", Audience: "Customers", Kind: "Standard", Detail: "Help with technical issues", InputTag: "tech_support", Category: "", OutputTag: "resolved", Solution: "Provide troubleshooting steps"}}}

		assert.Equal(t, expected, tabletData)
	})

	t.Run("TestGetContactReasonByID", func(t *testing.T) {
		contactReasonID := 1
		reason, err := dbClient.GetContactReasonByID(contactReasonID)
		assert.NoError(t, err)
		assert.NotNil(t, reason)

		expected := domain.ContactReason{
			ID:        1,
			Name:      "Technical Support",
			Audience:  "Customers",
			Kind:      "Standard",
			Detail:    "Help with technical issues",
			InputTag:  "tech_support",
			OutputTag: "resolved",
			Solution:  "Provide troubleshooting steps",
		}

		assert.Equal(t, expected, reason)
	})
	t.Run("TestGetStandByName", func(t *testing.T) {
		standName := "Main Office"
		stand, err := dbClient.GetStandByName(standName)
		assert.NoError(t, err)
		assert.NotNil(t, stand)

		expected := domain.Stand{
			ID:        1,
			Name:      "Main Office",
			CountryID: 1,
		}

		assert.Equal(t, expected, stand)
	})

	t.Run("TestGetStandByName - no stand", func(t *testing.T) {
		standName := "Non Existent Stand"
		expected := domain.Stand{}
		stand, err := dbClient.GetStandByName(standName)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found")
		assert.Equal(t, expected, stand)
	})

	t.Run("TestRepoGetWorkingAgentsByStandID", func(t *testing.T) {
		agents, err := dbClient.repo.GetWorkingAgentsByStandID(3)
		assert.NoError(t, err)
		assert.Len(t, agents, 1)

		err = dbClient.SetAgentIsWorkingByEmail(agents[0].Email, false)
		assert.NoError(t, err)

		agents, err = dbClient.repo.GetWorkingAgentsByStandID(3)
		assert.NoError(t, err)
		assert.Len(t, agents, 0)
	})

	t.Run("Create and Get Requester", func(t *testing.T) {
		requester := dto.Requester{
			Email:   "<EMAIL>",
			Name:    "John",
			Surname: "Doe",
			UserID:  sql.NullString{String: "cabify_user_123", Valid: true},
			Segment: sql.NullString{String: "T1", Valid: true},
		}
		err := dbClient.repo.CreateRequester(requester)
		assert.NoError(t, err)

		requesterFromDB, err := dbClient.repo.GetRequesterByEmail(requester.Email)
		requester.ID = requesterFromDB.ID
		assert.NoError(t, err)
		assert.Equal(t, requester, requesterFromDB)
	})

	t.Run("Create, Update and Get Appointment", func(t *testing.T) {
		// Create a new requester
		requester := dto.Requester{
			Email:   "<EMAIL>",
			Name:    "Jane",
			Surname: "Doe",
			UserID:  sql.NullString{String: "cabify_user_456", Valid: true},
			Segment: sql.NullString{String: "T1", Valid: true},
		}
		err := dbClient.repo.CreateRequester(requester)
		assert.NoError(t, err)

		requesterFromDB, err := dbClient.repo.GetRequesterByEmail(requester.Email)
		assert.NoError(t, err)
		requester.ID = requesterFromDB.ID

		// Create a new appointment with planned_at
		plannedTime := time.Date(2024, 6, 3, 22, 21, 0, 0, time.UTC)
		appointment := dto.Appointment{
			RequesterID:   requester.ID,
			LineID:        "AR01",
			ContactReason: 1,
			ZTicketID:     sql.NullInt64{Int64: 1, Valid: true},
			StandID:       1,
			Status:        "created",
			LastDo:        sql.NullTime{Time: plannedTime, Valid: true},
			PlannedAt:     sql.NullTime{Time: plannedTime, Valid: true},
			Origin:        "tablet",
		}
		err = dbClient.repo.SaveAppointment(appointment)
		assert.NoError(t, err)

		// Get the created appointment
		appointmentFromDB, err := dbClient.repo.GetAppointmentByID(1)
		assert.NoError(t, err)
		assert.Equal(t, appointment.RequesterID, appointmentFromDB.RequesterID)
		assert.Equal(t, appointment.LineID, appointmentFromDB.LineID)
		assert.Equal(t, appointment.ContactReason, appointmentFromDB.ContactReason)
		assert.Equal(t, appointment.ZTicketID, appointmentFromDB.ZTicketID)
		assert.Equal(t, appointment.Comments, appointmentFromDB.Comments)
		assert.Equal(t, appointment.StandID, appointmentFromDB.StandID)
		assert.Equal(t, appointment.AgentID, appointmentFromDB.AgentID)
		assert.Equal(t, appointment.Status, appointmentFromDB.Status)
		assert.Equal(t, appointment.LastDo, appointmentFromDB.LastDo)

		// Update the appointment
		appointmentFromDB.Comments = "Updated comments"
		appointmentFromDB.Status = domain.AttentionCancelled.String()
		err = dbClient.repo.SaveAppointment(appointmentFromDB)
		assert.NoError(t, err)

		// Get the updated appointment
		updatedAppointmentFromDB, err := dbClient.repo.GetAppointmentByID(appointmentFromDB.ID)
		assert.NoError(t, err)
		assert.Equal(t, appointmentFromDB.Comments, updatedAppointmentFromDB.Comments)
		assert.Equal(t, appointmentFromDB.Status, updatedAppointmentFromDB.Status)
	})

	t.Run("Save existing requester", func(t *testing.T) {
		// Create a new requester
		requester := dto.Requester{
			Email:   "<EMAIL>",
			Name:    "Jane",
			Surname: "Doe",
			UserID:  sql.NullString{String: "cabify_user_456", Valid: true},
			Segment: sql.NullString{String: "T1", Valid: true},
		}
		err := dbClient.repo.CreateRequester(requester)
		assert.NoError(t, err)

		// Create a new request
		plannedTime := time.Date(2024, 6, 3, 22, 21, 0, 0, time.UTC)
		request := domain.AttentionRequest{
			Requester: domain.Requester{
				Email:           requester.Email,
				Name:            requester.Name,
				Surname:         requester.Surname,
				LastDOOnRequest: plannedTime,
				Segment:         "T1",
			},
			LineID:        "TEST01",
			ContactReason: domain.ContactReason{ID: 2},
			Stand:         domain.Stand{ID: 2, Name: "Stand 2"},
			TicketID:      1234,
			PlannedAt:     plannedTime,
			Origin:        "tablet",
		}

		err = dbClient.SaveRequest(request)
		assert.NoError(t, err)
	})

	t.Run("Save new requester", func(t *testing.T) {
		// Create a new request
		plannedTime := time.Now()
		request := domain.AttentionRequest{
			Requester: domain.Requester{
				Email:   "<EMAIL>",
				Name:    "Jane",
				Surname: "Doe",
				Segment: "T1",
			},
			LineID:        "TEST02",
			ContactReason: domain.ContactReason{ID: 2},
			Stand:         domain.Stand{ID: 2, Name: "Stand 2"},
			TicketID:      1234,
			PlannedAt:     plannedTime,
			Origin:        "tablet",
		}

		err := dbClient.SaveRequest(request)
		assert.NoError(t, err)

		// Check if the requester was created
		requester, err := dbClient.repo.GetRequesterByEmail(request.Requester.Email)
		assert.NoError(t, err)
		assert.Equal(t, request.Requester.Email, requester.Email)
	})

	t.Run("Save Acuity request with planned_at and partner_id", func(t *testing.T) {
		// Create a new Acuity request with today's date
		now := time.Now()
		plannedTime := time.Date(now.Year(), now.Month(), now.Day(), 14, 30, 0, 0, time.UTC)
		request := domain.AttentionRequest{
			Requester: domain.Requester{
				Email:   "<EMAIL>",
				Name:    "Bob",
				Surname: "Acuity",
				Segment: "T1",
			},
			LineID:        "R-TEST03",
			ContactReason: domain.ContactReason{ID: 2},
			Stand:         domain.Stand{ID: 2, Name: "Stand 2"},
			TicketID:      5678,
			PlannedAt:     plannedTime,
			PartnerID:     12345,
		}

		err := dbClient.SaveAcuityRequest(request)
		assert.NoError(t, err)

		// Check if the requester was created
		requester, err := dbClient.repo.GetRequesterByEmail(request.Requester.Email)
		assert.NoError(t, err)
		assert.Equal(t, request.Requester.Email, requester.Email)

		// Check if the appointment was saved by getting it directly by ID
		// First, get all appointments to find our appointment ID
		appointments, err := dbClient.repo.GetStandDayAppointments(2, "UTC")
		assert.NoError(t, err)

		// Find our appointment
		var savedAppointment dto.Appointment
		found := false
		for _, app := range appointments {
			if app.LineID == "R-TEST03" {
				savedAppointment = app
				found = true
				break
			}
		}
		assert.True(t, found, "Acuity appointment should be found")
		assert.Equal(t, "acuity", savedAppointment.Origin)
		assert.True(t, savedAppointment.PlannedAt.Valid, "PlannedAt should be set")
		assert.Equal(t, plannedTime, savedAppointment.PlannedAt.Time)
		assert.True(t, savedAppointment.PartnerID.Valid, "PartnerID should be set")
		assert.Equal(t, int64(12345), savedAppointment.PartnerID.Int64)
	})

	t.Run("Get existing country by ID", func(t *testing.T) {
		// Test getting an existing country
		country, err := dbClient.GetCountryByID(2)
		assert.NoError(t, err)
		assert.Equal(t, "ESP", country.Name)
	})

	t.Run("Get non-existing country by ID", func(t *testing.T) {
		// Test getting a non-existing country
		_, err := dbClient.GetCountryByID(999)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "country 999 not found")
	})

	t.Run("GetStandCurrentMonthAppointmentsCount", func(t *testing.T) {
		// Test getting count with no appointments
		count, err := dbClient.GetStandCurrentMonthAppointmentsCount(3)
		assert.NoError(t, err)
		assert.Equal(t, 0, count)

		// Create two new appointments with planned_at
		now := time.Now()
		appointment1 := dto.Appointment{
			RequesterID:   1,
			LineID:        "line1",
			ContactReason: 1,
			StandID:       3,
			Status:        domain.AttentionCreated.String(),
			LastDo:        sql.NullTime{Time: now, Valid: true},
			PlannedAt:     sql.NullTime{Time: now, Valid: true},
			ZTicketID:     sql.NullInt64{Int64: 1, Valid: true},
			Origin:        "tablet",
		}
		err = dbClient.repo.SaveAppointment(appointment1)
		assert.NoError(t, err)

		appointment2 := dto.Appointment{
			RequesterID:   2,
			LineID:        "line2",
			ContactReason: 2,
			StandID:       3,
			Status:        domain.AttentionCreated.String(),
			LastDo:        sql.NullTime{Time: now, Valid: true},
			PlannedAt:     sql.NullTime{Time: now, Valid: true},
			ZTicketID:     sql.NullInt64{Int64: 2, Valid: true},
			Origin:        "tablet",
		}
		err = dbClient.repo.SaveAppointment(appointment2)
		assert.NoError(t, err)

		// Test getting count with two appointments
		count, err = dbClient.GetStandCurrentMonthAppointmentsCount(3)
		assert.NoError(t, err)
		assert.Equal(t, 2, count)
	})

	t.Run("Assign appointment", func(t *testing.T) {
		attendedTime := time.Now()
		appointment := domain.Appointment{
			ID:         1,
			AgentID:    1,
			Status:     domain.AttentionCalled,
			AttendedAt: attendedTime,
			StandID:    3,
		}

		err := dbClient.AssignAppointment(appointment)
		assert.NoError(t, err)

		appointmentFromDB, err := dbClient.repo.GetAppointmentByID(appointment.ID)
		assert.NoError(t, err)

		assert.Equal(t, appointment.AgentID, appointmentFromDB.AgentID)
		assert.Equal(t, domain.AttentionCalled.String(), appointmentFromDB.Status)
	})

	t.Run("Cancel appointment", func(t *testing.T) {
		appointment := domain.Appointment{
			ID:      2,
			AgentID: 1,
			Status:  domain.AttentionCancelled,
		}

		err := dbClient.CancelAppointment(appointment)
		assert.NoError(t, err)

		appointmentFromDB, err := dbClient.repo.GetAppointmentByID(appointment.ID)
		assert.NoError(t, err)

		assert.Equal(t, appointment.Status.String(), appointmentFromDB.Status)
	})

	t.Run("Set agent to working and get working agents and appointments", func(t *testing.T) {
		agentEmail := "<EMAIL>"
		err := dbClient.SetAgentIsWorkingByEmail(agentEmail, true)
		assert.NoError(t, err)

		lineData, err := dbClient.GetLineDataByAgentEmail(agentEmail)
		assert.NoError(t, err)
		isEqual := assert.Equal(t, 1, len(lineData.WorkingAgents))
		if !isEqual {
			t.Errorf("Error getting working agents: %v", err)
			return
		}
		assert.Equal(t, lineData.WorkingAgents[0].Email, agentEmail)
	})

	t.Run(("Set agent desk"), func(t *testing.T) {
		agentEmail := "<EMAIL>"
		desk := 3
		err := dbClient.SetAgentDeskByEmail(agentEmail, desk)
		assert.NoError(t, err)

		agent, err := dbClient.GetAgentByEmail(agentEmail)
		assert.NoError(t, err)
		assert.Equal(t, desk, agent.Desk)
	})

	t.Run("Get appointment by zendesk ticket ID", func(t *testing.T) {
		zendeskTicketID := 2
		appointment, err := dbClient.repo.GetAppointmentByZendeskID(zendeskTicketID)
		assert.NoError(t, err)
		assert.NotNil(t, appointment)
		assert.Equal(t, zendeskTicketID, int(appointment.ZTicketID.Int64))
	})

	t.Run("Get appointment by partner ID", func(t *testing.T) {
		// First, create an Acuity appointment with a partner_id
		now := time.Now()
		plannedTime := time.Date(now.Year(), now.Month(), now.Day(), 15, 30, 0, 0, time.UTC)
		request := domain.AttentionRequest{
			Requester: domain.Requester{
				Email:   "<EMAIL>",
				Name:    "Alice",
				Surname: "Partner",
				Segment: "T1",
			},
			LineID:        "R-PARTNER123",
			ContactReason: domain.ContactReason{ID: 1},
			Stand:         domain.Stand{ID: 1, Name: "Main Office"},
			TicketID:      9999,
			PlannedAt:     plannedTime,
			PartnerID:     54321,
		}

		err := dbClient.SaveAcuityRequest(request)
		assert.NoError(t, err)

		// Test GetAppointmentByPartnerID using the client method
		partnerID := 54321
		appointment, err := dbClient.GetAppointmentByPartnerID(partnerID)
		assert.NoError(t, err)
		assert.NotNil(t, appointment)
		assert.Equal(t, "R-PARTNER123", appointment.LineID)
		assert.Equal(t, 9999, appointment.ZendeskTicketID)

		// Test with non-existent partner ID
		_, err = dbClient.GetAppointmentByPartnerID(99999)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "appointment with partnerID 99999 not found")
	})
}

func setupTestingDatabase(t *testing.T) *Client {
	cfg, err := config.GetConfig()
	if err != nil {
		t.Fatalf("Error loading config: %v", err)
	}
	testConfig := cfg.DB
	// Skip test if db container is not available
	err = waitForDB(testConfig.Host, 5)
	if err != nil {
		t.Skip("Database container is not available")
		return nil
	}
	cfg.DB.MigrationsDir = "../../../migrations"

	db, err := RunMigrations(cfg.DB)
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}

	// Insert test data
	jsonFilePath := "../../../data/testData.json"
	jsonData, err := os.ReadFile(jsonFilePath)
	if err != nil {
		t.Fatalf("Failed to read JSON file: %v", err)
	}

	err = InsertDataFromJSON(db, jsonData)
	if err != nil {
		t.Errorf("Error inserting test data: %v", err)
	}

	// Clean database after run all tests
	t.Cleanup(func() {
		err := cleanDatabase(db)
		if err != nil {
			t.Fatalf("Error cleaning database: %v", err)
		}
		db.Close()
	})
	return NewClient(db)
}

func cleanDatabase(db *sql.DB) error {
	log.Infof("Cleaning database")
	// Deactivate foreign key checks
	_, err := db.Exec("SET FOREIGN_KEY_CHECKS = 0")
	if err != nil {
		return fmt.Errorf("error disabling foreign key checks: %v", err)
	}

	rows, err := db.Query("SHOW TABLES")
	if err != nil {
		return fmt.Errorf("error fetching tables: %v", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var table string
		if err := rows.Scan(&table); err != nil {
			return fmt.Errorf("error scanning table name: %v", err)
		}
		tables = append(tables, table)
	}

	// Delete all tables
	for _, table := range tables {
		_, err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table))
		if err != nil {
			return fmt.Errorf("error dropping table %s: %v", table, err)
		}
		log.Printf("Table %s dropped successfully", table)
	}

	// Reactivate foreign key checks
	_, err = db.Exec("SET FOREIGN_KEY_CHECKS = 1")
	if err != nil {
		return fmt.Errorf("error enabling foreign key checks: %v", err)
	}

	return nil
}

func waitForDB(host string, seconds time.Duration) error {
	timeout := seconds * time.Second
	deadline := time.Now().Add(timeout)

	for {
		conn, err := net.DialTimeout("tcp", fmt.Sprintf("%v:3306", host), timeout)
		if err == nil {
			conn.Close()
			log.Infof("Database is ready on! %v", conn.LocalAddr().String())
			return nil
		}

		if time.Now().After(deadline) {
			return fmt.Errorf("timeout waiting for database")
		}

		time.Sleep(1 * time.Second)
	}
}
