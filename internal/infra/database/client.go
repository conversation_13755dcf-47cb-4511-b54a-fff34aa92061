package database

import (
	"database/sql"
	"errors"
	"fmt"
	"os"
	"slices"
	"time"
	"turnero/internal/domain"
	"turnero/internal/infra/dto"

	"github.com/jmoiron/sqlx"
	log "github.com/sirupsen/logrus"
)

//go:generate mockery --outpkg mocks --name DatabaseClient
type DatabaseClient interface {
	GetTabletDataByAgentEmail(email string) (domain.TabletData, error)
	GetLineDataByAgentEmail(email string) (domain.LineData, error)
	GetLineDataByID(lineID int) (domain.LineData, error)
	GetLineDataByStandName(standName string) (domain.LineData, error)
	GetContactReasonByID(id int) (domain.ContactReason, error)
	GetContactReasonByName(name string) (domain.ContactReason, error)
	GetContactReasonsByStand(standID int) ([]domain.ContactReason, error)
	GetContactReasonIDsByStand(standID int) ([]int, error)
	SetStandContactReasons(standID int, contactReasonIDs []int) error
	GetAllContactReasons() ([]domain.ContactReason, error)
	CreateContactReason(contactReason domain.ContactReason) (int, error)
	UpdateContactReason(id int, contactReason domain.ContactReason) error
	DeleteContactReason(id int) error
	GetStandByName(name string) (domain.Stand, error)
	GetAllStands() ([]domain.Stand, error)
	CreateStand(stand domain.Stand) (int, error)
	UpdateStand(id int, stand domain.Stand) error
	DeleteStand(id int) error
	SaveRequest(request domain.AttentionRequest) error
	SaveAcuityRequest(request domain.AttentionRequest) error
	GetCountryByID(id int) (domain.Country, error)
	GetAllCountries() ([]domain.Country, error)
	GetCountryRelatedToStandID(standID int) (domain.Country, error)
	GetStandByID(id int) (domain.Stand, error)
	GetStandCurrentMonthAppointmentsCount(standID int) (int, error)
	GetAgentByEmail(email string) (domain.Agent, error)
	GetAgentByID(id int) (domain.Agent, error)
	GetAllAgents() ([]domain.Agent, error)
	CreateAgent(agent domain.Agent) (int, error)
	UpdateAgent(id int, agent domain.Agent) error
	DeleteAgent(id int) error
	SetAgentIsWorkingByEmail(email string, isWorking bool) error
	SetAgentDeskByEmail(email string, desk int) error
	SetAgentIsWorking(agentID int, isWorking bool) error
	SetAgentDesk(agentID int, desk int) error
	GetAgentUIDataByEmail(email string) (domain.AgentUIData, error)
	GetOpenAppointmentsByStandID(standID int, timezone string) ([]domain.Appointment, error)
	GetStandTimeZone(standID int) (string, error)
	GetRequesterByID(id int) (domain.Requester, error)
	AssignAppointment(appointment domain.Appointment) error
	CancelAppointment(appointment domain.Appointment) error
	GetAppointmentStatus(id int) (domain.AttentionStatus, error)
	ChangeStatusByZendeskID(zendeskID int, status domain.AttentionStatus) (domain.Appointment, error)
	GetAppointmentByPartnerID(partnerID int) (domain.Appointment, error)
	UpdateAcuityAppointment(partnerID int, newPlannedAt time.Time, newContactReasonID int) error
	ResetAgentsWorkingStatus() error
	GetAllStandNames() ([]string, error)
}

type Client struct {
	repo Repo
}

func NewClient(dataBase *sql.DB) *Client {
	repo := NewRepository(sqlx.NewDb(dataBase, "mysql"))
	return &Client{
		repo: repo,
	}
}

func (c *Client) GetLineDataByAgentEmail(email string) (domain.LineData, error) {
	// Get Agent's stand_id
	agent, err := c.repo.GetAgentByEmail(email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.LineData{}, fmt.Errorf("agent %s not found on Cabify directory", email)
		}
		return domain.LineData{}, err
	}
	return c.GetLineDataByID(int(agent.StandID.Int64))
}

func (c *Client) GetLineDataByID(lineID int) (domain.LineData, error) {
	// Get working agents of the stand
	rawWorkingAgents, err := c.repo.GetWorkingAgentsByStandID(lineID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.LineData{}, fmt.Errorf("stand %d has no working agents", lineID)
		}
		return domain.LineData{}, err
	}
	var workingAgents []domain.Agent
	for _, rawAgent := range rawWorkingAgents {
		var desk int64
		if rawAgent.Desk.Valid {
			desk = rawAgent.Desk.Int64
		}
		workingAgents = append(workingAgents, domain.Agent{
			ID:      rawAgent.ID,
			Email:   rawAgent.Email,
			StandID: int(rawAgent.StandID.Int64),
			Desk:    int(desk),
		})
	}
	// Get Stand Data
	stand, err := c.repo.GetStandByID(lineID)
	if err != nil {
		return domain.LineData{}, err
	}

	timeZone, err := c.GetStandTimeZone(stand.ID)
	if err != nil {
		return domain.LineData{}, err
	}

	// Get Stand's Day Appointments
	rawAppointments, err := c.repo.GetStandDayAppointments(stand.ID, timeZone)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.LineData{}, fmt.Errorf("stand %d has no appointments", stand.ID)
		}
		return domain.LineData{}, err
	}

	var appointments []domain.Appointment
	for _, rawAppointment := range rawAppointments {
		// Get Contact Reason Details by ID
		reason, err := c.GetContactReasonByID(rawAppointment.ContactReason)
		if err != nil {
			return domain.LineData{}, err
		}
		appointments = append(appointments, domain.Appointment{
			ID:            rawAppointment.ID,
			LineID:        rawAppointment.LineID,
			ContactReason: reason,
			CreatedAt:     rawAppointment.CreatedAt.Time,
			PlannedAt:     rawAppointment.PlannedAt.Time,
			AgentID:       rawAppointment.AgentID,
			Status:        domain.AttentionStatus(rawAppointment.Status),
			Origin:        rawAppointment.Origin,
		})
	}

	return domain.LineData{
		Stand:         domain.Stand{ID: stand.ID, Name: stand.Name},
		WorkingAgents: workingAgents,
		Appointments:  appointments,
	}, nil
}

func (c *Client) GetLineDataByStandName(standName string) (domain.LineData, error) {
	// Get Stand Data
	stand, err := c.repo.GetStandByName(standName)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.LineData{}, fmt.Errorf("stand %s not found", standName)
		}
		return domain.LineData{}, err
	}

	return c.GetLineDataByID(stand.ID)
}

func (c *Client) GetTabletDataByAgentEmail(email string) (domain.TabletData, error) {
	// Get Agent's stand_id

	if os.Getenv("SERVANT_ENVIRONMENT") == "local" {
		email = "<EMAIL>"
	}

	agent, err := c.repo.GetAgentByEmail(email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.TabletData{}, fmt.Errorf("agent %s not found on Cabify directory", email)
		}
		return domain.TabletData{}, err
	}

	if !agent.StandID.Valid {
		return domain.TabletData{}, fmt.Errorf("agent %s has no stand", email)
	}

	// Get Stand's Country
	stand, err := c.repo.GetStandByID(int(agent.StandID.Int64))
	if err != nil {
		return domain.TabletData{}, err
	}

	// Get Stand's Contact Reasons
	rawContactReasons, err := c.repo.GetContactReasonsByStand(stand.ID)
	if err != nil || len(rawContactReasons) == 0 {
		// If no stand-specific contact reasons found or error, fallback to all contact reasons
		rawContactReasons, err = c.repo.GetAllContactReasons()
		if err != nil {
			return domain.TabletData{}, err
		}
	}

	contactReasons := rawContactReasonsToDomain(rawContactReasons)

	return domain.TabletData{
		Stand:          stand.Name,
		ContactReasons: contactReasons,
	}, nil
}

func rawContactReasonsToDomain(rawReasons []dto.ContactReason) []domain.ContactReason {
	reasons := make([]domain.ContactReason, 0, len(rawReasons))
	for _, rawReason := range rawReasons {
		reasons = append(reasons, domain.ContactReason{
			ID:        rawReason.ID,
			Name:      rawReason.Name,
			Audience:  rawReason.Audience,
			Kind:      rawReason.Kind,
			Detail:    rawReason.Detail.String,
			InputTag:  rawReason.InputTag.String,
			Category:  rawReason.Category.String,
			OutputTag: rawReason.OutputTag.String,
			Solution:  rawReason.Solution.String,
		})
	}
	return reasons
}

func (c *Client) GetContactReasonByID(id int) (domain.ContactReason, error) {
	rawReason, err := c.repo.GetContactReasonByID(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.ContactReason{}, fmt.Errorf("contact reason %d not found", id)
		}
		return domain.ContactReason{}, err
	}
	return domain.ContactReason{
		ID:        rawReason.ID,
		Name:      rawReason.Name,
		Audience:  rawReason.Audience,
		Kind:      rawReason.Kind,
		Detail:    rawReason.Detail.String,
		InputTag:  rawReason.InputTag.String,
		OutputTag: rawReason.OutputTag.String,
		Solution:  rawReason.Solution.String,
		Category:  rawReason.Category.String,
	}, nil
}

func (c *Client) GetContactReasonByName(name string) (domain.ContactReason, error) {
	rawReason, err := c.repo.GetContactReasonByName(name)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.ContactReason{}, fmt.Errorf("contact reason %s not found", name)
		}
		return domain.ContactReason{}, err
	}
	return domain.ContactReason{
		ID:        rawReason.ID,
		Name:      rawReason.Name,
		Audience:  rawReason.Audience,
		Kind:      rawReason.Kind,
		Detail:    rawReason.Detail.String,
		InputTag:  rawReason.InputTag.String,
		OutputTag: rawReason.OutputTag.String,
		Solution:  rawReason.Solution.String,
		Category:  rawReason.Category.String,
	}, nil
}

func (c *Client) GetAllContactReasons() ([]domain.ContactReason, error) {
	rawReasons, err := c.repo.GetAllContactReasons()
	if err != nil {
		return nil, err
	}
	return rawContactReasonsToDomain(rawReasons), nil
}

func (c *Client) CreateContactReason(contactReason domain.ContactReason) (int, error) {
	dtoContactReason := dto.ContactReason{
		Name:      contactReason.Name,
		Audience:  contactReason.Audience,
		Kind:      contactReason.Kind,
		Detail:    sql.NullString{String: contactReason.Detail, Valid: contactReason.Detail != ""},
		InputTag:  sql.NullString{String: contactReason.InputTag, Valid: contactReason.InputTag != ""},
		OutputTag: sql.NullString{String: contactReason.OutputTag, Valid: contactReason.OutputTag != ""},
		Solution:  sql.NullString{String: contactReason.Solution, Valid: contactReason.Solution != ""},
		Category:  sql.NullString{String: contactReason.Category, Valid: contactReason.Category != ""},
	}
	return c.repo.CreateContactReason(dtoContactReason)
}

func (c *Client) UpdateContactReason(id int, contactReason domain.ContactReason) error {
	dtoContactReason := dto.ContactReason{
		Name:      contactReason.Name,
		Audience:  contactReason.Audience,
		Kind:      contactReason.Kind,
		Detail:    sql.NullString{String: contactReason.Detail, Valid: contactReason.Detail != ""},
		InputTag:  sql.NullString{String: contactReason.InputTag, Valid: contactReason.InputTag != ""},
		OutputTag: sql.NullString{String: contactReason.OutputTag, Valid: contactReason.OutputTag != ""},
		Solution:  sql.NullString{String: contactReason.Solution, Valid: contactReason.Solution != ""},
		Category:  sql.NullString{String: contactReason.Category, Valid: contactReason.Category != ""},
	}
	return c.repo.UpdateContactReason(id, dtoContactReason)
}

func (c *Client) DeleteContactReason(id int) error {
	return c.repo.DeleteContactReason(id)
}

func (c *Client) GetContactReasonsByStand(standID int) ([]domain.ContactReason, error) {
	rawContactReasons, err := c.repo.GetContactReasonsByStand(standID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return []domain.ContactReason{}, fmt.Errorf("no contact reasons found for stand %d", standID)
		}
		return []domain.ContactReason{}, err
	}
	return rawContactReasonsToDomain(rawContactReasons), nil
}

func (c *Client) GetContactReasonIDsByStand(standID int) ([]int, error) {
	return c.repo.GetContactReasonIDsByStand(standID)
}

func (c *Client) SetStandContactReasons(standID int, contactReasonIDs []int) error {
	return c.repo.SetStandContactReasons(standID, contactReasonIDs)
}

func (c *Client) GetStandByName(name string) (domain.Stand, error) {
	rawStand, err := c.repo.GetStandByName(name)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Stand{}, fmt.Errorf("stand %s not found", name)
		}
		return domain.Stand{}, err
	}
	return domain.Stand{
		ID:        rawStand.ID,
		Name:      rawStand.Name,
		CountryID: rawStand.CountryID,
	}, nil
}

func (c *Client) SaveRequest(request domain.AttentionRequest) error {
	//Search requester by email
	requester, err := c.repo.GetRequesterByEmail(request.Requester.Email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// If does not exist, create it
			var cabifyUserID sql.NullString
			if request.Requester.UserID != "" {
				cabifyUserID = sql.NullString{
					String: request.Requester.UserID,
					Valid:  true,
				}
			}
			var requesterSegment sql.NullString
			if request.Requester.Segment != "" {
				requesterSegment = sql.NullString{
					String: request.Requester.Segment,
					Valid:  true,
				}
			}
			err := c.repo.CreateRequester(dto.Requester{
				Email:   request.Requester.Email,
				Name:    request.Requester.Name,
				Surname: request.Requester.Surname,
				UserID:  cabifyUserID,
				Segment: requesterSegment,
			})
			if err != nil {
				return err
			}
			requester, err = c.repo.GetRequesterByEmail(request.Requester.Email)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}
	//Save the appointment
	var lastDO sql.NullTime
	if !request.Requester.LastDOOnRequest.IsZero() {
		lastDO = sql.NullTime{
			Time:  request.Requester.LastDOOnRequest,
			Valid: true,
		}
	}

	// For tablet appointments, planned_at is the same as created_at (current time)
	now := time.Now()
	appointment := dto.Appointment{
		RequesterID:   requester.ID,
		LineID:        request.LineID,
		ContactReason: request.ContactReason.ID,
		StandID:       request.Stand.ID,
		Status:        domain.AttentionCreated.String(),
		LastDo:        lastDO,
		ZTicketID:     sql.NullInt64{Int64: int64(request.TicketID), Valid: true},
		PlannedAt:     sql.NullTime{Time: now, Valid: true},
		Origin:        "tablet",
	}

	err = c.repo.SaveAppointment(appointment)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) GetCountryByID(id int) (domain.Country, error) {
	rawCountry, err := c.repo.GetCountryByID(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Country{}, fmt.Errorf("country %d not found", id)
		}
		return domain.Country{}, err
	}
	return domain.Country{
		ID:   rawCountry.ID,
		Name: rawCountry.Name,
	}, nil
}

func (c *Client) GetAllCountries() ([]domain.Country, error) {
	rawCountries, err := c.repo.GetAllCountries()
	if err != nil {
		return nil, err
	}

	countries := make([]domain.Country, len(rawCountries))
	for i, rawCountry := range rawCountries {
		countries[i] = domain.Country{
			ID:   rawCountry.ID,
			Name: rawCountry.Name,
		}
	}
	return countries, nil
}

func (c *Client) GetCountryRelatedToStandID(standID int) (domain.Country, error) {
	country, err := c.repo.CountryRelatedToStandID(standID)
	if err != nil {
		return domain.Country{}, err
	}
	return domain.Country{
		ID:   country.ID,
		Name: country.Name,
	}, nil
}

func (c *Client) GetStandCurrentMonthAppointmentsCount(standID int) (int, error) {
	if standID == 0 {
		return 0, fmt.Errorf("standID is required")
	}

	total, err := c.repo.GetStandCurrentMonthAppointmentsCount(standID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, nil // No appointments found
		}
		return 0, err
	}

	return total, nil
}

func (c *Client) SetAgentIsWorkingByEmail(email string, isWorking bool) error {
	agent, err := c.repo.GetAgentByEmail(email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("agent %s not found", email)
		}
		return err
	}

	err = c.repo.SetAgentIsWorking(agent.ID, isWorking)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) SetAgentIsWorking(agentID int, isWorking bool) error {
	return c.repo.SetAgentIsWorking(agentID, isWorking)
}

func (c *Client) SetAgentDesk(agentID int, desk int) error {
	return c.repo.SetAgentDesk(agentID, desk)
}

func (c *Client) GetAgentByEmail(email string) (domain.Agent, error) {
	rawAgent, err := c.repo.GetAgentByEmail(email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Agent{}, fmt.Errorf("agent %s not found", email)
		}
		return domain.Agent{}, err
	}
	var standID int
	if rawAgent.StandID.Valid {
		standID = int(rawAgent.StandID.Int64)
	}

	var desk int
	if rawAgent.Desk.Valid {
		desk = int(rawAgent.Desk.Int64)
	}

	return domain.Agent{
		ID:        rawAgent.ID,
		Email:     rawAgent.Email,
		StandID:   standID,
		IsWorking: rawAgent.IsWorking,
		Desk:      desk,
		Role:      rawAgent.Role,
	}, nil
}

func (c *Client) GetAgentByID(id int) (domain.Agent, error) {
	rawAgent, err := c.repo.GetAgentByID(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Agent{}, fmt.Errorf("agent %d not found", id)
		}
		return domain.Agent{}, err
	}
	var standID int
	if rawAgent.StandID.Valid {
		standID = int(rawAgent.StandID.Int64)
	}

	var desk int
	if rawAgent.Desk.Valid {
		desk = int(rawAgent.Desk.Int64)
	}

	return domain.Agent{
		ID:        rawAgent.ID,
		Email:     rawAgent.Email,
		StandID:   standID,
		IsWorking: rawAgent.IsWorking,
		Desk:      desk,
		Role:      rawAgent.Role,
	}, nil
}

func (c *Client) GetAllAgents() ([]domain.Agent, error) {
	rawAgents, err := c.repo.GetAllAgents()
	if err != nil {
		return nil, err
	}

	agents := make([]domain.Agent, len(rawAgents))
	for i, rawAgent := range rawAgents {
		var standID int
		if rawAgent.StandID.Valid {
			standID = int(rawAgent.StandID.Int64)
		}

		var desk int
		if rawAgent.Desk.Valid {
			desk = int(rawAgent.Desk.Int64)
		}

		agents[i] = domain.Agent{
			ID:        rawAgent.ID,
			Email:     rawAgent.Email,
			StandID:   standID,
			IsWorking: rawAgent.IsWorking,
			Desk:      desk,
			Role:      rawAgent.Role,
		}
	}
	return agents, nil
}

func (c *Client) CreateAgent(agent domain.Agent) (int, error) {
	dtoAgent := dto.Agent{
		Email:     agent.Email,
		StandID:   sql.NullInt64{Int64: int64(agent.StandID), Valid: agent.StandID != 0},
		IsWorking: agent.IsWorking,
		Desk:      sql.NullInt64{Int64: int64(agent.Desk), Valid: agent.Desk != 0},
		Role:      agent.Role,
	}
	return c.repo.CreateAgent(dtoAgent)
}

func (c *Client) UpdateAgent(id int, agent domain.Agent) error {
	dtoAgent := dto.Agent{
		Email:     agent.Email,
		StandID:   sql.NullInt64{Int64: int64(agent.StandID), Valid: agent.StandID != 0},
		IsWorking: agent.IsWorking,
		Desk:      sql.NullInt64{Int64: int64(agent.Desk), Valid: agent.Desk != 0},
		Role:      agent.Role,
	}
	return c.repo.UpdateAgent(id, dtoAgent)
}

func (c *Client) DeleteAgent(id int) error {
	return c.repo.DeleteAgent(id)
}

func (c *Client) GetStandByID(id int) (domain.Stand, error) {
	rawStand, err := c.repo.GetStandByID(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Stand{}, fmt.Errorf("stand %d not found", id)
		}
		return domain.Stand{}, err
	}
	selectedDesks, err := c.repo.GetStandSelectedDesksByItsAgents(id)
	slices.Sort(selectedDesks)

	if err != nil {
		return domain.Stand{}, err
	}
	return domain.Stand{
		ID:            rawStand.ID,
		Name:          rawStand.Name,
		CountryID:     rawStand.CountryID,
		SelectedDesks: selectedDesks,
	}, nil
}

func (c *Client) GetOpenAppointmentsByStandID(standID int, timezone string) ([]domain.Appointment, error) {
	rawAppointments, err := c.repo.GetStandDayAppointments(standID, timezone)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	var appointments []domain.Appointment
	for _, rawAppointment := range rawAppointments {
		// Skip closed appointments
		if rawAppointment.Status == domain.AttentionClosed.String() || rawAppointment.Status == domain.AttentionCancelled.String() {
			continue
		}
		// Get Contact Reason Details by ID
		reason, err := c.GetContactReasonByID(rawAppointment.ContactReason)
		if err != nil {
			return nil, err
		}
		//Get Requester by ID
		requester, err := c.GetRequesterByID(rawAppointment.RequesterID)
		if err != nil {
			return nil, err
		}
		var zendeskTicketID int
		if rawAppointment.ZTicketID.Valid {
			zendeskTicketID = int(rawAppointment.ZTicketID.Int64)
		}

		appointments = append(appointments, domain.Appointment{
			ID:              rawAppointment.ID,
			LineID:          rawAppointment.LineID,
			ContactReason:   reason,
			CreatedAt:       rawAppointment.CreatedAt.Time,
			PlannedAt:       rawAppointment.PlannedAt.Time,
			AgentID:         rawAppointment.AgentID,
			Status:          domain.AttentionStatus(rawAppointment.Status),
			Requester:       requester,
			ZendeskTicketID: zendeskTicketID,
			StandID:         rawAppointment.StandID,
			Origin:          rawAppointment.Origin,
		})
	}

	return appointments, nil
}

func (c *Client) GetAgentUIDataByEmail(email string) (domain.AgentUIData, error) {
	agent, err := c.GetAgentByEmail(email)
	if err != nil {
		return domain.AgentUIData{}, err
	}

	stand, err := c.GetStandByID(agent.StandID)
	if err != nil {
		return domain.AgentUIData{}, err
	}

	timeZone, err := c.GetStandTimeZone(stand.ID)
	if err != nil {
		return domain.AgentUIData{}, err
	}

	openAppointments, err := c.GetOpenAppointmentsByStandID(stand.ID, timeZone)
	if err != nil {
		return domain.AgentUIData{}, err
	}

	// Remove the appointments that are already addressed by other agents
	ownAndFreeAppointments := filterUnassignedOrOwnAppointments(openAppointments, agent.ID)

	return domain.AgentUIData{
		Agent:            agent,
		Stand:            stand,
		OpenAppointments: ownAndFreeAppointments,
	}, nil
}

func (c *Client) GetStandTimeZone(standID int) (string, error) {
	stand, err := c.GetStandByID(standID)
	if err != nil {
		return "", err
	}

	country, err := c.GetCountryByID(stand.CountryID)
	if err != nil {
		return "", err
	}

	return isoAlpha3ToTimeZone(country.Name), nil
}

func (c *Client) GetRequesterByID(id int) (domain.Requester, error) {
	rawRequester, err := c.repo.GetRequesterByID(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Requester{}, fmt.Errorf("requester %d not found", id)
		}
		return domain.Requester{}, err
	}
	var segment string
	if rawRequester.Segment.Valid {
		segment = rawRequester.Segment.String
	}

	var userID string
	if rawRequester.UserID.Valid {
		userID = rawRequester.UserID.String
	}

	return domain.Requester{
		ID:      rawRequester.ID,
		Name:    rawRequester.Name,
		Surname: rawRequester.Surname,
		Email:   rawRequester.Email,
		Segment: segment,
		UserID:  userID,
	}, nil
}

func (c *Client) SetAgentDeskByEmail(email string, desk int) error {
	agent, err := c.repo.GetAgentByEmail(email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("agent %s not found", email)
		}
		return err
	}

	err = c.repo.SetAgentDesk(agent.ID, desk)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) AssignAppointment(appointment domain.Appointment) error {
	// Get the actual appointment
	rawAppointment, err := c.repo.GetAppointmentByID(appointment.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("appointment %d not found", appointment.ID)
		}
		return err
	}

	// Compare the appointment with the new one and update the fields that changed
	if rawAppointment.StandID != appointment.StandID {
		rawAppointment.StandID = appointment.StandID
	}
	if rawAppointment.AgentID != appointment.AgentID {
		rawAppointment.AgentID = appointment.AgentID
	}
	if rawAppointment.Status != appointment.Status.String() {
		rawAppointment.Status = appointment.Status.String()
	}
	if rawAppointment.AttendedAt.Time != appointment.AttendedAt {
		rawAppointment.AttendedAt = sql.NullTime{Time: appointment.AttendedAt, Valid: true}
	}

	err = c.repo.SaveAppointment(rawAppointment)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) CancelAppointment(appointment domain.Appointment) error {
	// Get the actual appointment
	rawAppointment, err := c.repo.GetAppointmentByID(appointment.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("appointment %d not found", appointment.ID)
		}
		return err
	}

	// Update the appointment status
	rawAppointment.Status = domain.AttentionCancelled.String()
	err = c.repo.SaveAppointment(rawAppointment)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) GetAppointmentStatus(id int) (domain.AttentionStatus, error) {
	rawAppointment, err := c.repo.GetAppointmentByID(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.AttentionUndefined, fmt.Errorf("appointment %d not found", id)
		}
		return domain.AttentionUndefined, err
	}
	return domain.AttentionStatus(rawAppointment.Status), nil
}

func (c *Client) ChangeStatusByZendeskID(zendeskID int, status domain.AttentionStatus) (domain.Appointment, error) {
	rawAppointment, err := c.repo.GetAppointmentByZendeskID(zendeskID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Appointment{}, fmt.Errorf("appointment with zendeskID %d not found", zendeskID)
		}
		return domain.Appointment{}, err
	}

	rawAppointment.Status = status.String()
	err = c.repo.SaveAppointment(rawAppointment)
	if err != nil {
		return domain.Appointment{}, err
	}
	return domain.Appointment{
		ID:      rawAppointment.ID,
		StandID: rawAppointment.StandID,
		AgentID: rawAppointment.AgentID,
		Status:  status,
	}, nil
}

func (c *Client) GetAppointmentByPartnerID(partnerID int) (domain.Appointment, error) {
	rawAppointment, err := c.repo.GetAppointmentByPartnerID(partnerID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return domain.Appointment{}, fmt.Errorf("appointment with partnerID %d not found", partnerID)
		}
		return domain.Appointment{}, err
	}

	// Get Contact Reason Details by ID
	contactReason, err := c.GetContactReasonByID(rawAppointment.ContactReason)
	if err != nil {
		return domain.Appointment{}, err
	}

	// Convert to domain appointment
	appointment := domain.Appointment{
		ID:              rawAppointment.ID,
		LineID:          rawAppointment.LineID,
		ContactReason:   contactReason,
		StandID:         rawAppointment.StandID,
		AgentID:         rawAppointment.AgentID,
		Status:          domain.AttentionStatus(rawAppointment.Status),
		ZendeskTicketID: int(rawAppointment.ZTicketID.Int64),
	}

	return appointment, nil
}

func (c *Client) ResetAgentsWorkingStatus() error {
	return c.repo.CloseAllWorkingTurnsAndUnsetDesks()
}

func (c *Client) GetAllStands() ([]domain.Stand, error) {
	rawStands, err := c.repo.GetAllStands()
	if err != nil {
		return nil, err
	}

	stands := make([]domain.Stand, len(rawStands))
	for i, rawStand := range rawStands {
		selectedDesks, err := c.repo.GetStandSelectedDesksByItsAgents(rawStand.ID)
		if err != nil {
			return nil, err
		}
		slices.Sort(selectedDesks)

		stands[i] = domain.Stand{
			ID:            rawStand.ID,
			Name:          rawStand.Name,
			CountryID:     rawStand.CountryID,
			SelectedDesks: selectedDesks,
		}
	}
	return stands, nil
}

func (c *Client) CreateStand(stand domain.Stand) (int, error) {
	dtoStand := dto.Stand{
		Name:      stand.Name,
		CountryID: stand.CountryID,
	}
	return c.repo.CreateStand(dtoStand)
}

func (c *Client) UpdateStand(id int, stand domain.Stand) error {
	dtoStand := dto.Stand{
		Name:      stand.Name,
		CountryID: stand.CountryID,
	}
	return c.repo.UpdateStand(id, dtoStand)
}

func (c *Client) DeleteStand(id int) error {
	return c.repo.DeleteStand(id)
}

func (c *Client) GetAllStandNames() ([]string, error) {
	standNames, err := c.repo.GetAllStands()
	if err != nil {
		return nil, err
	}
	if len(standNames) == 0 {
		return nil, fmt.Errorf("no stands found")
	}

	// Extract stand names from the slice of structs
	names := make([]string, len(standNames))
	for i, stand := range standNames {
		names[i] = stand.Name
	}
	return names, nil
}

func isoAlpha3ToTimeZone(isoAlpha3 string) string {
	if len(isoAlpha3) != 3 {
		log.Errorf("Invalid ISO Alpha-3 code: %s", isoAlpha3)
		return "UTC"
	}
	switch isoAlpha3 {
	case "ARG":
		return "America/Argentina/Buenos_Aires"
	case "BRA":
		return "America/Sao_Paulo"
	case "CHL":
		return "America/Santiago"
	case "COL":
		return "America/Bogota"
	case "ESP":
		return "Europe/Madrid"
	case "MEX":
		return "America/Mexico_City"
	case "PER":
		return "America/Lima"
	case "URY":
		return "America/Montevideo"
	default:
		return "UTC"
	}
}

func filterUnassignedOrOwnAppointments(openAppointments []domain.Appointment, agentID int) []domain.Appointment {
	filteredAppointments := make([]domain.Appointment, 0, len(openAppointments))
	for _, appointment := range openAppointments {
		if appointment.AgentID == 0 || appointment.AgentID == agentID {
			filteredAppointments = append(filteredAppointments, appointment)
		}
	}

	return filteredAppointments
}

// SaveAcuityRequest saves an Acuity appointment with planned_at and partner_id
func (c *Client) SaveAcuityRequest(request domain.AttentionRequest) error {
	// Search requester by email
	requester, err := c.repo.GetRequesterByEmail(request.Requester.Email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// If does not exist, create it
			var cabifyUserID sql.NullString
			if request.Requester.UserID != "" {
				cabifyUserID = sql.NullString{
					String: request.Requester.UserID,
					Valid:  true,
				}
			}
			var requesterSegment sql.NullString
			if request.Requester.Segment != "" {
				requesterSegment = sql.NullString{
					String: request.Requester.Segment,
					Valid:  true,
				}
			}
			err := c.repo.CreateRequester(dto.Requester{
				Email:   request.Requester.Email,
				Name:    request.Requester.Name,
				Surname: request.Requester.Surname,
				UserID:  cabifyUserID,
				Segment: requesterSegment,
			})
			if err != nil {
				return err
			}
			requester, err = c.repo.GetRequesterByEmail(request.Requester.Email)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Save the appointment with Acuity-specific data
	var lastDO sql.NullTime
	if !request.Requester.LastDOOnRequest.IsZero() {
		lastDO = sql.NullTime{
			Time:  request.Requester.LastDOOnRequest,
			Valid: true,
		}
	}

	appointment := dto.Appointment{
		RequesterID:   requester.ID,
		LineID:        request.LineID,
		ContactReason: request.ContactReason.ID,
		StandID:       request.Stand.ID,
		Status:        domain.AttentionCreated.String(),
		LastDo:        lastDO,
		ZTicketID:     sql.NullInt64{Int64: int64(request.TicketID), Valid: true},
		PlannedAt:     sql.NullTime{Time: request.PlannedAt, Valid: true}, // Use Acuity's planned time
		Origin:        "acuity",
		PartnerID:     sql.NullInt64{Int64: int64(request.PartnerID), Valid: true}, // Store Acuity appointment ID
	}

	err = c.repo.SaveAppointment(appointment)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) UpdateAcuityAppointment(partnerID int, newPlannedAt time.Time, newContactReasonID int) error {
	// Get the existing appointment by partner ID
	rawAppointment, err := c.repo.GetAppointmentByPartnerID(partnerID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("appointment with partner ID %d not found", partnerID)
		}
		return err
	}

	// Update the planned_at time and contact_reason
	rawAppointment.PlannedAt = sql.NullTime{Time: newPlannedAt, Valid: true}
	rawAppointment.ContactReason = newContactReasonID

	// Save the updated appointment
	err = c.repo.SaveAppointment(rawAppointment)
	if err != nil {
		return err
	}

	return nil
}
