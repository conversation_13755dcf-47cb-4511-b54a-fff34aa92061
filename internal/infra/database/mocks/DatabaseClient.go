// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	domain "turnero/internal/domain"

	mock "github.com/stretchr/testify/mock"

	time "time"
)

// DatabaseClient is an autogenerated mock type for the DatabaseClient type
type DatabaseClient struct {
	mock.Mock
}

// AssignAppointment provides a mock function with given fields: appointment
func (_m *DatabaseClient) AssignAppointment(appointment domain.Appointment) error {
	ret := _m.Called(appointment)

	if len(ret) == 0 {
		panic("no return value specified for AssignAppointment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.Appointment) error); ok {
		r0 = rf(appointment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CancelAppointment provides a mock function with given fields: appointment
func (_m *DatabaseClient) CancelAppointment(appointment domain.Appointment) error {
	ret := _m.Called(appointment)

	if len(ret) == 0 {
		panic("no return value specified for CancelAppointment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.Appointment) error); ok {
		r0 = rf(appointment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ChangeStatusByZendeskID provides a mock function with given fields: zendeskID, status
func (_m *DatabaseClient) ChangeStatusByZendeskID(zendeskID int, status domain.AttentionStatus) (domain.Appointment, error) {
	ret := _m.Called(zendeskID, status)

	if len(ret) == 0 {
		panic("no return value specified for ChangeStatusByZendeskID")
	}

	var r0 domain.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int, domain.AttentionStatus) (domain.Appointment, error)); ok {
		return rf(zendeskID, status)
	}
	if rf, ok := ret.Get(0).(func(int, domain.AttentionStatus) domain.Appointment); ok {
		r0 = rf(zendeskID, status)
	} else {
		r0 = ret.Get(0).(domain.Appointment)
	}

	if rf, ok := ret.Get(1).(func(int, domain.AttentionStatus) error); ok {
		r1 = rf(zendeskID, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateAgent provides a mock function with given fields: agent
func (_m *DatabaseClient) CreateAgent(agent domain.Agent) (int, error) {
	ret := _m.Called(agent)

	if len(ret) == 0 {
		panic("no return value specified for CreateAgent")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(domain.Agent) (int, error)); ok {
		return rf(agent)
	}
	if rf, ok := ret.Get(0).(func(domain.Agent) int); ok {
		r0 = rf(agent)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(domain.Agent) error); ok {
		r1 = rf(agent)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateContactReason provides a mock function with given fields: contactReason
func (_m *DatabaseClient) CreateContactReason(contactReason domain.ContactReason) (int, error) {
	ret := _m.Called(contactReason)

	if len(ret) == 0 {
		panic("no return value specified for CreateContactReason")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(domain.ContactReason) (int, error)); ok {
		return rf(contactReason)
	}
	if rf, ok := ret.Get(0).(func(domain.ContactReason) int); ok {
		r0 = rf(contactReason)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(domain.ContactReason) error); ok {
		r1 = rf(contactReason)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateStand provides a mock function with given fields: stand
func (_m *DatabaseClient) CreateStand(stand domain.Stand) (int, error) {
	ret := _m.Called(stand)

	if len(ret) == 0 {
		panic("no return value specified for CreateStand")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(domain.Stand) (int, error)); ok {
		return rf(stand)
	}
	if rf, ok := ret.Get(0).(func(domain.Stand) int); ok {
		r0 = rf(stand)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(domain.Stand) error); ok {
		r1 = rf(stand)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteAgent provides a mock function with given fields: id
func (_m *DatabaseClient) DeleteAgent(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAgent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteContactReason provides a mock function with given fields: id
func (_m *DatabaseClient) DeleteContactReason(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteContactReason")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteStand provides a mock function with given fields: id
func (_m *DatabaseClient) DeleteStand(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteStand")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAgentByEmail provides a mock function with given fields: email
func (_m *DatabaseClient) GetAgentByEmail(email string) (domain.Agent, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetAgentByEmail")
	}

	var r0 domain.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.Agent, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) domain.Agent); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(domain.Agent)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAgentByID provides a mock function with given fields: id
func (_m *DatabaseClient) GetAgentByID(id int) (domain.Agent, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetAgentByID")
	}

	var r0 domain.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Agent, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Agent); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(domain.Agent)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAgentUIDataByEmail provides a mock function with given fields: email
func (_m *DatabaseClient) GetAgentUIDataByEmail(email string) (domain.AgentUIData, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetAgentUIDataByEmail")
	}

	var r0 domain.AgentUIData
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.AgentUIData, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) domain.AgentUIData); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(domain.AgentUIData)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllAgents provides a mock function with no fields
func (_m *DatabaseClient) GetAllAgents() ([]domain.Agent, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllAgents")
	}

	var r0 []domain.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]domain.Agent, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []domain.Agent); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Agent)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllContactReasons provides a mock function with no fields
func (_m *DatabaseClient) GetAllContactReasons() ([]domain.ContactReason, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllContactReasons")
	}

	var r0 []domain.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]domain.ContactReason, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []domain.ContactReason); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.ContactReason)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllCountries provides a mock function with no fields
func (_m *DatabaseClient) GetAllCountries() ([]domain.Country, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllCountries")
	}

	var r0 []domain.Country
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]domain.Country, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []domain.Country); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Country)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllStandNames provides a mock function with no fields
func (_m *DatabaseClient) GetAllStandNames() ([]string, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllStandNames")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]string, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllStands provides a mock function with no fields
func (_m *DatabaseClient) GetAllStands() ([]domain.Stand, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllStands")
	}

	var r0 []domain.Stand
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]domain.Stand, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []domain.Stand); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Stand)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAppointmentByPartnerID provides a mock function with given fields: partnerID
func (_m *DatabaseClient) GetAppointmentByPartnerID(partnerID int) (domain.Appointment, error) {
	ret := _m.Called(partnerID)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentByPartnerID")
	}

	var r0 domain.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Appointment, error)); ok {
		return rf(partnerID)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Appointment); ok {
		r0 = rf(partnerID)
	} else {
		r0 = ret.Get(0).(domain.Appointment)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(partnerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAppointmentStatus provides a mock function with given fields: id
func (_m *DatabaseClient) GetAppointmentStatus(id int) (domain.AttentionStatus, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentStatus")
	}

	var r0 domain.AttentionStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.AttentionStatus, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) domain.AttentionStatus); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(domain.AttentionStatus)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonByID provides a mock function with given fields: id
func (_m *DatabaseClient) GetContactReasonByID(id int) (domain.ContactReason, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonByID")
	}

	var r0 domain.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.ContactReason, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) domain.ContactReason); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(domain.ContactReason)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonByName provides a mock function with given fields: name
func (_m *DatabaseClient) GetContactReasonByName(name string) (domain.ContactReason, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonByName")
	}

	var r0 domain.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.ContactReason, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) domain.ContactReason); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(domain.ContactReason)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonIDsByStand provides a mock function with given fields: standID
func (_m *DatabaseClient) GetContactReasonIDsByStand(standID int) ([]int, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonIDsByStand")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]int, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) []int); ok {
		r0 = rf(standID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonsByStand provides a mock function with given fields: standID
func (_m *DatabaseClient) GetContactReasonsByStand(standID int) ([]domain.ContactReason, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonsByStand")
	}

	var r0 []domain.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]domain.ContactReason, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) []domain.ContactReason); ok {
		r0 = rf(standID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.ContactReason)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCountryByID provides a mock function with given fields: id
func (_m *DatabaseClient) GetCountryByID(id int) (domain.Country, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetCountryByID")
	}

	var r0 domain.Country
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Country, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Country); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(domain.Country)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCountryRelatedToStandID provides a mock function with given fields: standID
func (_m *DatabaseClient) GetCountryRelatedToStandID(standID int) (domain.Country, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetCountryRelatedToStandID")
	}

	var r0 domain.Country
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Country, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Country); ok {
		r0 = rf(standID)
	} else {
		r0 = ret.Get(0).(domain.Country)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLineDataByAgentEmail provides a mock function with given fields: email
func (_m *DatabaseClient) GetLineDataByAgentEmail(email string) (domain.LineData, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetLineDataByAgentEmail")
	}

	var r0 domain.LineData
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.LineData, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) domain.LineData); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(domain.LineData)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLineDataByID provides a mock function with given fields: lineID
func (_m *DatabaseClient) GetLineDataByID(lineID int) (domain.LineData, error) {
	ret := _m.Called(lineID)

	if len(ret) == 0 {
		panic("no return value specified for GetLineDataByID")
	}

	var r0 domain.LineData
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.LineData, error)); ok {
		return rf(lineID)
	}
	if rf, ok := ret.Get(0).(func(int) domain.LineData); ok {
		r0 = rf(lineID)
	} else {
		r0 = ret.Get(0).(domain.LineData)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(lineID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLineDataByStandName provides a mock function with given fields: standName
func (_m *DatabaseClient) GetLineDataByStandName(standName string) (domain.LineData, error) {
	ret := _m.Called(standName)

	if len(ret) == 0 {
		panic("no return value specified for GetLineDataByStandName")
	}

	var r0 domain.LineData
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.LineData, error)); ok {
		return rf(standName)
	}
	if rf, ok := ret.Get(0).(func(string) domain.LineData); ok {
		r0 = rf(standName)
	} else {
		r0 = ret.Get(0).(domain.LineData)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(standName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOpenAppointmentsByStandID provides a mock function with given fields: standID, timezone
func (_m *DatabaseClient) GetOpenAppointmentsByStandID(standID int, timezone string) ([]domain.Appointment, error) {
	ret := _m.Called(standID, timezone)

	if len(ret) == 0 {
		panic("no return value specified for GetOpenAppointmentsByStandID")
	}

	var r0 []domain.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string) ([]domain.Appointment, error)); ok {
		return rf(standID, timezone)
	}
	if rf, ok := ret.Get(0).(func(int, string) []domain.Appointment); ok {
		r0 = rf(standID, timezone)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Appointment)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(standID, timezone)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRequesterByID provides a mock function with given fields: id
func (_m *DatabaseClient) GetRequesterByID(id int) (domain.Requester, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetRequesterByID")
	}

	var r0 domain.Requester
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Requester, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Requester); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(domain.Requester)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandByID provides a mock function with given fields: id
func (_m *DatabaseClient) GetStandByID(id int) (domain.Stand, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetStandByID")
	}

	var r0 domain.Stand
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Stand, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Stand); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(domain.Stand)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandByName provides a mock function with given fields: name
func (_m *DatabaseClient) GetStandByName(name string) (domain.Stand, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetStandByName")
	}

	var r0 domain.Stand
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.Stand, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) domain.Stand); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(domain.Stand)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandCurrentMonthAppointmentsCount provides a mock function with given fields: standID
func (_m *DatabaseClient) GetStandCurrentMonthAppointmentsCount(standID int) (int, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetStandCurrentMonthAppointmentsCount")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(standID)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandTimeZone provides a mock function with given fields: standID
func (_m *DatabaseClient) GetStandTimeZone(standID int) (string, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetStandTimeZone")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (string, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) string); ok {
		r0 = rf(standID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTabletDataByAgentEmail provides a mock function with given fields: email
func (_m *DatabaseClient) GetTabletDataByAgentEmail(email string) (domain.TabletData, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetTabletDataByAgentEmail")
	}

	var r0 domain.TabletData
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.TabletData, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) domain.TabletData); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(domain.TabletData)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ResetAgentsWorkingStatus provides a mock function with no fields
func (_m *DatabaseClient) ResetAgentsWorkingStatus() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ResetAgentsWorkingStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveAcuityRequest provides a mock function with given fields: request
func (_m *DatabaseClient) SaveAcuityRequest(request domain.AttentionRequest) error {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for SaveAcuityRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.AttentionRequest) error); ok {
		r0 = rf(request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveRequest provides a mock function with given fields: request
func (_m *DatabaseClient) SaveRequest(request domain.AttentionRequest) error {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for SaveRequest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.AttentionRequest) error); ok {
		r0 = rf(request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetAgentDesk provides a mock function with given fields: agentID, desk
func (_m *DatabaseClient) SetAgentDesk(agentID int, desk int) error {
	ret := _m.Called(agentID, desk)

	if len(ret) == 0 {
		panic("no return value specified for SetAgentDesk")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int) error); ok {
		r0 = rf(agentID, desk)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetAgentDeskByEmail provides a mock function with given fields: email, desk
func (_m *DatabaseClient) SetAgentDeskByEmail(email string, desk int) error {
	ret := _m.Called(email, desk)

	if len(ret) == 0 {
		panic("no return value specified for SetAgentDeskByEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, int) error); ok {
		r0 = rf(email, desk)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetAgentIsWorking provides a mock function with given fields: agentID, isWorking
func (_m *DatabaseClient) SetAgentIsWorking(agentID int, isWorking bool) error {
	ret := _m.Called(agentID, isWorking)

	if len(ret) == 0 {
		panic("no return value specified for SetAgentIsWorking")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, bool) error); ok {
		r0 = rf(agentID, isWorking)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetAgentIsWorkingByEmail provides a mock function with given fields: email, isWorking
func (_m *DatabaseClient) SetAgentIsWorkingByEmail(email string, isWorking bool) error {
	ret := _m.Called(email, isWorking)

	if len(ret) == 0 {
		panic("no return value specified for SetAgentIsWorkingByEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, bool) error); ok {
		r0 = rf(email, isWorking)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetStandContactReasons provides a mock function with given fields: standID, contactReasonIDs
func (_m *DatabaseClient) SetStandContactReasons(standID int, contactReasonIDs []int) error {
	ret := _m.Called(standID, contactReasonIDs)

	if len(ret) == 0 {
		panic("no return value specified for SetStandContactReasons")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, []int) error); ok {
		r0 = rf(standID, contactReasonIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateAcuityAppointment provides a mock function with given fields: partnerID, newPlannedAt, newContactReasonID
func (_m *DatabaseClient) UpdateAcuityAppointment(partnerID int, newPlannedAt time.Time, newContactReasonID int) error {
	ret := _m.Called(partnerID, newPlannedAt, newContactReasonID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAcuityAppointment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, time.Time, int) error); ok {
		r0 = rf(partnerID, newPlannedAt, newContactReasonID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateAgent provides a mock function with given fields: id, agent
func (_m *DatabaseClient) UpdateAgent(id int, agent domain.Agent) error {
	ret := _m.Called(id, agent)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAgent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.Agent) error); ok {
		r0 = rf(id, agent)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateContactReason provides a mock function with given fields: id, contactReason
func (_m *DatabaseClient) UpdateContactReason(id int, contactReason domain.ContactReason) error {
	ret := _m.Called(id, contactReason)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContactReason")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.ContactReason) error); ok {
		r0 = rf(id, contactReason)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateStand provides a mock function with given fields: id, stand
func (_m *DatabaseClient) UpdateStand(id int, stand domain.Stand) error {
	ret := _m.Called(id, stand)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStand")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, domain.Stand) error); ok {
		r0 = rf(id, stand)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewDatabaseClient creates a new instance of DatabaseClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDatabaseClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *DatabaseClient {
	mock := &DatabaseClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
