// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	dto "turnero/internal/infra/dto"

	mock "github.com/stretchr/testify/mock"
)

// Repo is an autogenerated mock type for the Repo type
type Repo struct {
	mock.Mock
}

// CloseAllWorkingTurnsAndUnsetDesks provides a mock function with no fields
func (_m *Repo) CloseAllWorkingTurnsAndUnsetDesks() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for CloseAllWorkingTurnsAndUnsetDesks")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CountryRelatedToStandID provides a mock function with given fields: id
func (_m *Repo) CountryRelatedToStandID(id int) (dto.Country, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for CountryRelatedToStandID")
	}

	var r0 dto.Country
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Country, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Country); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.Country)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateAgent provides a mock function with given fields: agent
func (_m *Repo) CreateAgent(agent dto.Agent) (int, error) {
	ret := _m.Called(agent)

	if len(ret) == 0 {
		panic("no return value specified for CreateAgent")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(dto.Agent) (int, error)); ok {
		return rf(agent)
	}
	if rf, ok := ret.Get(0).(func(dto.Agent) int); ok {
		r0 = rf(agent)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(dto.Agent) error); ok {
		r1 = rf(agent)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateContactReason provides a mock function with given fields: contactReason
func (_m *Repo) CreateContactReason(contactReason dto.ContactReason) (int, error) {
	ret := _m.Called(contactReason)

	if len(ret) == 0 {
		panic("no return value specified for CreateContactReason")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(dto.ContactReason) (int, error)); ok {
		return rf(contactReason)
	}
	if rf, ok := ret.Get(0).(func(dto.ContactReason) int); ok {
		r0 = rf(contactReason)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(dto.ContactReason) error); ok {
		r1 = rf(contactReason)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRequester provides a mock function with given fields: requester
func (_m *Repo) CreateRequester(requester dto.Requester) error {
	ret := _m.Called(requester)

	if len(ret) == 0 {
		panic("no return value specified for CreateRequester")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(dto.Requester) error); ok {
		r0 = rf(requester)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateStand provides a mock function with given fields: stand
func (_m *Repo) CreateStand(stand dto.Stand) (int, error) {
	ret := _m.Called(stand)

	if len(ret) == 0 {
		panic("no return value specified for CreateStand")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(dto.Stand) (int, error)); ok {
		return rf(stand)
	}
	if rf, ok := ret.Get(0).(func(dto.Stand) int); ok {
		r0 = rf(stand)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(dto.Stand) error); ok {
		r1 = rf(stand)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteAgent provides a mock function with given fields: id
func (_m *Repo) DeleteAgent(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAgent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteContactReason provides a mock function with given fields: id
func (_m *Repo) DeleteContactReason(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteContactReason")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteStand provides a mock function with given fields: id
func (_m *Repo) DeleteStand(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteStand")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAgentByEmail provides a mock function with given fields: email
func (_m *Repo) GetAgentByEmail(email string) (dto.Agent, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetAgentByEmail")
	}

	var r0 dto.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (dto.Agent, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) dto.Agent); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(dto.Agent)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAgentByID provides a mock function with given fields: id
func (_m *Repo) GetAgentByID(id int) (dto.Agent, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetAgentByID")
	}

	var r0 dto.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Agent, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Agent); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.Agent)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllAgents provides a mock function with no fields
func (_m *Repo) GetAllAgents() ([]dto.Agent, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllAgents")
	}

	var r0 []dto.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]dto.Agent, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []dto.Agent); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.Agent)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllContactReasons provides a mock function with no fields
func (_m *Repo) GetAllContactReasons() ([]dto.ContactReason, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllContactReasons")
	}

	var r0 []dto.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]dto.ContactReason, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []dto.ContactReason); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.ContactReason)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllCountries provides a mock function with no fields
func (_m *Repo) GetAllCountries() ([]dto.Country, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllCountries")
	}

	var r0 []dto.Country
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]dto.Country, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []dto.Country); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.Country)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllStands provides a mock function with no fields
func (_m *Repo) GetAllStands() ([]dto.Stand, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllStands")
	}

	var r0 []dto.Stand
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]dto.Stand, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []dto.Stand); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.Stand)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAppointmentByID provides a mock function with given fields: id
func (_m *Repo) GetAppointmentByID(id int) (dto.Appointment, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentByID")
	}

	var r0 dto.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Appointment, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Appointment); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.Appointment)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAppointmentByPartnerID provides a mock function with given fields: partnerID
func (_m *Repo) GetAppointmentByPartnerID(partnerID int) (dto.Appointment, error) {
	ret := _m.Called(partnerID)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentByPartnerID")
	}

	var r0 dto.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Appointment, error)); ok {
		return rf(partnerID)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Appointment); ok {
		r0 = rf(partnerID)
	} else {
		r0 = ret.Get(0).(dto.Appointment)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(partnerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAppointmentByZendeskID provides a mock function with given fields: id
func (_m *Repo) GetAppointmentByZendeskID(id int) (dto.Appointment, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetAppointmentByZendeskID")
	}

	var r0 dto.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Appointment, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Appointment); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.Appointment)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonByID provides a mock function with given fields: id
func (_m *Repo) GetContactReasonByID(id int) (dto.ContactReason, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonByID")
	}

	var r0 dto.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.ContactReason, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.ContactReason); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.ContactReason)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonByName provides a mock function with given fields: name
func (_m *Repo) GetContactReasonByName(name string) (dto.ContactReason, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonByName")
	}

	var r0 dto.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (dto.ContactReason, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) dto.ContactReason); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(dto.ContactReason)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonIDsByStand provides a mock function with given fields: standID
func (_m *Repo) GetContactReasonIDsByStand(standID int) ([]int, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonIDsByStand")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]int, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) []int); ok {
		r0 = rf(standID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonsByCountry provides a mock function with given fields: countryID
func (_m *Repo) GetContactReasonsByCountry(countryID int) ([]dto.ContactReason, error) {
	ret := _m.Called(countryID)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonsByCountry")
	}

	var r0 []dto.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]dto.ContactReason, error)); ok {
		return rf(countryID)
	}
	if rf, ok := ret.Get(0).(func(int) []dto.ContactReason); ok {
		r0 = rf(countryID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.ContactReason)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(countryID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContactReasonsByStand provides a mock function with given fields: standID
func (_m *Repo) GetContactReasonsByStand(standID int) ([]dto.ContactReason, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetContactReasonsByStand")
	}

	var r0 []dto.ContactReason
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]dto.ContactReason, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) []dto.ContactReason); ok {
		r0 = rf(standID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.ContactReason)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCountryByID provides a mock function with given fields: id
func (_m *Repo) GetCountryByID(id int) (dto.Country, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetCountryByID")
	}

	var r0 dto.Country
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Country, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Country); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.Country)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRequesterByEmail provides a mock function with given fields: email
func (_m *Repo) GetRequesterByEmail(email string) (dto.Requester, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetRequesterByEmail")
	}

	var r0 dto.Requester
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (dto.Requester, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) dto.Requester); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(dto.Requester)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRequesterByID provides a mock function with given fields: id
func (_m *Repo) GetRequesterByID(id int) (dto.Requester, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetRequesterByID")
	}

	var r0 dto.Requester
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Requester, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Requester); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(dto.Requester)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandByID provides a mock function with given fields: standID
func (_m *Repo) GetStandByID(standID int) (dto.Stand, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetStandByID")
	}

	var r0 dto.Stand
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (dto.Stand, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) dto.Stand); ok {
		r0 = rf(standID)
	} else {
		r0 = ret.Get(0).(dto.Stand)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandByName provides a mock function with given fields: name
func (_m *Repo) GetStandByName(name string) (dto.Stand, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetStandByName")
	}

	var r0 dto.Stand
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (dto.Stand, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) dto.Stand); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(dto.Stand)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandCurrentMonthAppointmentsCount provides a mock function with given fields: standID
func (_m *Repo) GetStandCurrentMonthAppointmentsCount(standID int) (int, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetStandCurrentMonthAppointmentsCount")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(standID)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandDayAppointments provides a mock function with given fields: standID, timezone
func (_m *Repo) GetStandDayAppointments(standID int, timezone string) ([]dto.Appointment, error) {
	ret := _m.Called(standID, timezone)

	if len(ret) == 0 {
		panic("no return value specified for GetStandDayAppointments")
	}

	var r0 []dto.Appointment
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string) ([]dto.Appointment, error)); ok {
		return rf(standID, timezone)
	}
	if rf, ok := ret.Get(0).(func(int, string) []dto.Appointment); ok {
		r0 = rf(standID, timezone)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.Appointment)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(standID, timezone)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStandSelectedDesksByItsAgents provides a mock function with given fields: standID
func (_m *Repo) GetStandSelectedDesksByItsAgents(standID int) ([]int, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetStandSelectedDesksByItsAgents")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]int, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) []int); ok {
		r0 = rf(standID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetWorkingAgentsByStandID provides a mock function with given fields: standID
func (_m *Repo) GetWorkingAgentsByStandID(standID int) ([]dto.Agent, error) {
	ret := _m.Called(standID)

	if len(ret) == 0 {
		panic("no return value specified for GetWorkingAgentsByStandID")
	}

	var r0 []dto.Agent
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]dto.Agent, error)); ok {
		return rf(standID)
	}
	if rf, ok := ret.Get(0).(func(int) []dto.Agent); ok {
		r0 = rf(standID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dto.Agent)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(standID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveAppointment provides a mock function with given fields: appointment
func (_m *Repo) SaveAppointment(appointment dto.Appointment) error {
	ret := _m.Called(appointment)

	if len(ret) == 0 {
		panic("no return value specified for SaveAppointment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(dto.Appointment) error); ok {
		r0 = rf(appointment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetAgentDesk provides a mock function with given fields: agentID, desk
func (_m *Repo) SetAgentDesk(agentID int, desk int) error {
	ret := _m.Called(agentID, desk)

	if len(ret) == 0 {
		panic("no return value specified for SetAgentDesk")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int) error); ok {
		r0 = rf(agentID, desk)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetAgentIsWorking provides a mock function with given fields: agentID, isWorking
func (_m *Repo) SetAgentIsWorking(agentID int, isWorking bool) error {
	ret := _m.Called(agentID, isWorking)

	if len(ret) == 0 {
		panic("no return value specified for SetAgentIsWorking")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, bool) error); ok {
		r0 = rf(agentID, isWorking)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetStandContactReasons provides a mock function with given fields: standID, contactReasonIDs
func (_m *Repo) SetStandContactReasons(standID int, contactReasonIDs []int) error {
	ret := _m.Called(standID, contactReasonIDs)

	if len(ret) == 0 {
		panic("no return value specified for SetStandContactReasons")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, []int) error); ok {
		r0 = rf(standID, contactReasonIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateAgent provides a mock function with given fields: id, agent
func (_m *Repo) UpdateAgent(id int, agent dto.Agent) error {
	ret := _m.Called(id, agent)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAgent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, dto.Agent) error); ok {
		r0 = rf(id, agent)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateContactReason provides a mock function with given fields: id, contactReason
func (_m *Repo) UpdateContactReason(id int, contactReason dto.ContactReason) error {
	ret := _m.Called(id, contactReason)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContactReason")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, dto.ContactReason) error); ok {
		r0 = rf(id, contactReason)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateStand provides a mock function with given fields: id, stand
func (_m *Repo) UpdateStand(id int, stand dto.Stand) error {
	ret := _m.Called(id, stand)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStand")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, dto.Stand) error); ok {
		r0 = rf(id, stand)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewRepo creates a new instance of Repo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repo {
	mock := &Repo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
