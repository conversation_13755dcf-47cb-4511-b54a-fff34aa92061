package database

import (
	"database/sql"
	"errors"
	"fmt"
	"turnero/internal/config"

	_ "github.com/go-sql-driver/mysql"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	log "github.com/sirupsen/logrus"
)

func RunMigrations(conf *config.MysqlConfig) (*sql.DB, error) {
	// Open DB connection
	connectionString := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?%s",
		conf.User, conf.Password, conf.Host, conf.Port, conf.Database, conf.Options)
	log.Infof("Connecting to host %v on port %v to database %v", conf.Host, conf.Port, conf.Database)

	dbConnection, err := sql.Open("mysql", connectionString)
	if err != nil {
		log.Errorf("Could not connect to database for running migrations: %v", err)
		return nil, err
	}
	log.Infof("Connected to database %v", conf.Database)

	driver, err := mysql.WithInstance(dbConnection, &mysql.Config{})
	if err != nil {
		log.Errorf("Error instanciating driver %v", err)
		return nil, err
	}
	log.Infof("Driver instanciated")

	// Run migrations
	migrationsPath := fmt.Sprintf("file://%s", conf.MigrationsDir)
	dbInstance, err := migrate.NewWithDatabaseInstance(
		migrationsPath,
		conf.Host,
		driver,
	)
	if err != nil && !errors.Is(err, migrate.ErrNoChange) {
		log.Errorf("Error instanciating DB instance %v:", err)
		return nil, err
	}

	version, dirty, err := dbInstance.Version()
	if err != nil && !errors.Is(err, migrate.ErrNilVersion) {
		log.Fatalf("Could not get the current version of the migration database instance: %v", err)
	}
	log.Infof("Current database migration version: %v (dirty %t)", version, dirty)

	if dirty {
		err = dbInstance.Force(int(version))
		if err != nil {
			log.Fatalf("Could not force the database to the latest valid version: %v", err)
		}
	}

	err = dbInstance.Up()
	if err != nil && !errors.Is(err, migrate.ErrNoChange) {
		log.Errorf("Error instancing up DB %v", err)
	}
	return dbConnection, nil
}
