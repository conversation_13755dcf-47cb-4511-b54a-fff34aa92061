package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"turnero/internal/infra/dto"
)

func InsertDataFromJSON(db *sql.DB, jsonData []byte) error {
	var data dto.JSONDBData
	err := json.Unmarshal(jsonData, &data)
	if err != nil {
		return fmt.Errorf("error unmarshaling JSON: %w", err)
	}

	// Insert countries
	for _, country := range data.Countries {
		_, err := db.Exec(`INSERT INTO countries (id, name) VALUES (?, ?) ON DUPLICATE KEY UPDATE name=VALUES(name)`, country.ID, country.Name)
		if err != nil {
			return fmt.Errorf("error inserting country: %w", err)
		}
	}

	// Insert contact reasons
	for _, reason := range data.ContactReasons {
		_, err := db.Exec(`INSERT INTO contact_reasons (id, name, audience, kind, detail, input_tag, output_tag, solution, category, is_deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE name=VALUES(name), audience=VALUES(audience), kind=VALUES(kind), detail=VALUES(detail), input_tag=VALUES(input_tag), output_tag=VALUES(output_tag), solution=VALUES(solution), category=VALUES(category), is_deleted=VALUES(is_deleted)`,
			reason.ID, reason.Name, reason.Audience, reason.Kind, reason.Detail, reason.InputTag, reason.OutputTag, reason.Solution, reason.Category, reason.IsDeleted)
		if err != nil {
			return fmt.Errorf("error inserting contact reason: %w", err)
		}
	}
	// Insert stands
	for _, stand := range data.Stands {
		_, err := db.Exec(`INSERT INTO stands (id, name, country_id) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE name=VALUES(name), country_id=VALUES(country_id)`, stand.ID, stand.Name, stand.CountryID)
		if err != nil {
			return fmt.Errorf("error inserting stand: %w", err)
		}
	}

	// Insert agents
	for _, agent := range data.Agents {
		role := agent.Role
		if role == "" {
			role = "agent" // Default role
		}
		_, err := db.Exec(`INSERT INTO agents (id, email, stand_id, is_working, desk, role) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE email=VALUES(email), stand_id=VALUES(stand_id),is_working=VALUES(is_working), desk=VALUES(desk), role=VALUES(role)`, agent.ID, agent.Email, agent.StandID, agent.IsWorking, agent.Desk, role)
		if err != nil {
			return fmt.Errorf("error inserting agent: %w", err)
		}
	}

	// Insert contact_reasons_countries
	for _, crc := range data.ContactReasonsCountries {
		var exists bool
		err := db.QueryRow(`SELECT EXISTS(SELECT 1 FROM contact_reasons_countries WHERE contact_reason_id = ? AND country_id = ?)`, crc.ContactReasonID, crc.CountryID).Scan(&exists)
		if err != nil {
			return fmt.Errorf("error checking existence of contact reason-country relation: %w", err)
		}

		if !exists {
			_, err = db.Exec(`INSERT INTO contact_reasons_countries (contact_reason_id, country_id) VALUES (?, ?)`, crc.ContactReasonID, crc.CountryID)
			if err != nil {
				return fmt.Errorf("error inserting contact reason-country relation: %w", err)
			}
		}
	}

	// Insert contact_reasons_stands
	for _, crs := range data.ContactReasonsStands {
		var exists bool
		err := db.QueryRow(`SELECT EXISTS(SELECT 1 FROM contact_reasons_stands WHERE contact_reason_id = ? AND stand_id = ?)`, crs.ContactReasonID, crs.StandID).Scan(&exists)
		if err != nil {
			return fmt.Errorf("error checking existence of contact reason-stand relation: %w", err)
		}

		if !exists {
			_, err = db.Exec(`INSERT INTO contact_reasons_stands (contact_reason_id, stand_id, is_deleted) VALUES (?, ?, FALSE)`, crs.ContactReasonID, crs.StandID)
			if err != nil {
				return fmt.Errorf("error inserting contact reason-stand relation: %w", err)
			}
		}
	}

	return nil
}
