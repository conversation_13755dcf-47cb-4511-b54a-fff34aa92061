package database

import (
	"database/sql"
	"errors"
	"fmt"
	"testing"
	"time"
	"turnero/internal/domain"
	mockRepo "turnero/internal/infra/database/mocks"
	"turnero/internal/infra/dto"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestGetTabletDataByAgentEmail(t *testing.T) {
	tests := []struct {
		name           string
		email          string
		agent          dto.Agent
		stand          dto.Stand
		contactReasons []dto.ContactReason
		mockAgentErr   error
		mockStandErr   error
		mockContactErr error
		expectedErr    error
		expectedResult domain.TabletData
	}{
		{
			name:  "Valid agent with stand",
			email: "<EMAIL>",
			agent: dto.Agent{
				StandID: sql.NullInt64{Int64: 1, Valid: true},
			},
			stand: dto.Stand{
				Name:      "Stand1",
				CountryID: 1,
			},
			contactReasons: []dto.ContactReason{
				{ID: 1, Name: "Reason1", Audience: "Audience1", Kind: "Kind1"},
			},
			expectedErr: nil,
			expectedResult: domain.TabletData{
				Stand: "Stand1",
				ContactReasons: []domain.ContactReason{
					{ID: 1, Name: "Reason1", Audience: "Audience1", Kind: "Kind1"},
				},
			},
		},
		{
			name:  "Agent without stand",
			email: "<EMAIL>",
			agent: dto.Agent{
				StandID: sql.NullInt64{Valid: false},
			},
			expectedErr: fmt.Errorf("agent <EMAIL> has no stand"),
		},
		{
			name:         "Error retrieving agent",
			email:        "<EMAIL>",
			mockAgentErr: errors.New("error retrieving agent"),
			expectedErr:  errors.New("error retrieving agent"),
		},
		{
			name:         "Error retrieving stand",
			email:        "<EMAIL>",
			agent:        dto.Agent{StandID: sql.NullInt64{Int64: 1, Valid: true}},
			mockStandErr: errors.New("error retrieving stand"),
			expectedErr:  errors.New("error retrieving stand"),
		},
		{
			name:           "Error retrieving contact reasons",
			email:          "<EMAIL>",
			agent:          dto.Agent{StandID: sql.NullInt64{Int64: 1, Valid: true}},
			stand:          dto.Stand{Name: "Stand1", CountryID: 1},
			mockContactErr: errors.New("error retrieving contact reasons"),
			expectedErr:    errors.New("error retrieving contact reasons"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetAgentByEmail", tt.email).Return(tt.agent, tt.mockAgentErr)

			if tt.agent.StandID.Valid && tt.mockAgentErr == nil {
				repo.On("GetStandByID", int(tt.agent.StandID.Int64)).Return(tt.stand, tt.mockStandErr)
				if tt.mockStandErr == nil {
					// First try to get stand-specific contact reasons, then fallback to all
					repo.On("GetContactReasonsByStand", tt.stand.ID).Return([]dto.ContactReason{}, nil)
					repo.On("GetAllContactReasons").Return(tt.contactReasons, tt.mockContactErr)
				}
			}

			// Call the method
			result, err := client.GetTabletDataByAgentEmail(tt.email)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestGetContactReasonsByStand(t *testing.T) {
	tests := []struct {
		name            string
		standID         int
		mockReasons     []dto.ContactReason
		mockErr         error
		expectedErr     error
		expectedReasons []domain.ContactReason
	}{
		{
			name:    "Valid stand with contact reasons",
			standID: 1,
			mockReasons: []dto.ContactReason{
				{
					ID:       1,
					Name:     "Technical Support",
					Audience: "Customers",
					Kind:     "Standard",
					Detail:   sql.NullString{String: "Help with technical issues", Valid: true},
					InputTag: sql.NullString{String: "tech_support", Valid: true},
				},
			},
			expectedErr: nil,
			expectedReasons: []domain.ContactReason{
				{
					ID:       1,
					Name:     "Technical Support",
					Audience: "Customers",
					Kind:     "Standard",
					Detail:   "Help with technical issues",
					InputTag: "tech_support",
				},
			},
		},
		{
			name:            "Stand with no contact reasons",
			standID:         2,
			mockErr:         sql.ErrNoRows,
			expectedErr:     errors.New("no contact reasons found for stand 2"),
			expectedReasons: []domain.ContactReason{},
		},
		{
			name:            "Database error",
			standID:         3,
			mockErr:         errors.New("database error"),
			expectedErr:     errors.New("database error"),
			expectedReasons: []domain.ContactReason{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetContactReasonsByStand", tt.standID).Return(tt.mockReasons, tt.mockErr)

			// Call the method
			result, err := client.GetContactReasonsByStand(tt.standID)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedReasons, result)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestRawContactReasonsToDomain(t *testing.T) {
	tests := []struct {
		name     string
		input    []dto.ContactReason
		expected []domain.ContactReason
	}{
		{
			name: "Convert single contact reason with all fields set",
			input: []dto.ContactReason{
				{
					ID:        1,
					Name:      "Reason 1",
					Audience:  "Audience 1",
					Kind:      "Kind 1",
					Detail:    sql.NullString{String: "Detail 1", Valid: true},
					InputTag:  sql.NullString{String: "InputTag 1", Valid: true},
					OutputTag: sql.NullString{String: "OutputTag 1", Valid: true},
					Solution:  sql.NullString{String: "Solution 1", Valid: true},
				},
			},
			expected: []domain.ContactReason{
				{
					ID:        1,
					Name:      "Reason 1",
					Audience:  "Audience 1",
					Kind:      "Kind 1",
					Detail:    "Detail 1",
					InputTag:  "InputTag 1",
					OutputTag: "OutputTag 1",
					Solution:  "Solution 1",
				},
			},
		},
		{
			name: "Convert single contact reason with null fields",
			input: []dto.ContactReason{
				{
					ID:        2,
					Name:      "Reason 2",
					Audience:  "Audience 2",
					Kind:      "Kind 2",
					Detail:    sql.NullString{Valid: false},
					InputTag:  sql.NullString{Valid: false},
					OutputTag: sql.NullString{Valid: false},
					Solution:  sql.NullString{Valid: false},
				},
			},
			expected: []domain.ContactReason{
				{
					ID:        2,
					Name:      "Reason 2",
					Audience:  "Audience 2",
					Kind:      "Kind 2",
					Detail:    "",
					InputTag:  "",
					OutputTag: "",
					Solution:  "",
				},
			},
		},
		{
			name:     "Convert empty slice",
			input:    []dto.ContactReason{},
			expected: []domain.ContactReason{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := rawContactReasonsToDomain(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetAgentByEmail(t *testing.T) {
	tests := []struct {
		name          string
		email         string
		mockAgent     dto.Agent
		mockErr       error
		expectedErr   error
		expectedAgent domain.Agent
	}{
		{
			name:  "Valid agent",
			email: "<EMAIL>",
			mockAgent: dto.Agent{
				ID:      1,
				Email:   "<EMAIL>",
				StandID: sql.NullInt64{Int64: 1, Valid: true},
				Desk:    sql.NullInt64{Int64: 10, Valid: true},
			},
			expectedErr: nil,
			expectedAgent: domain.Agent{
				ID:      1,
				Email:   "<EMAIL>",
				StandID: 1,
				Desk:    10,
			},
		},
		{
			name:        "Agent not found",
			email:       "<EMAIL>",
			mockErr:     sql.ErrNoRows,
			expectedErr: errors.New("agent <EMAIL> not found"),
		},
		{
			name:        "Database error",
			email:       "<EMAIL>",
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
		{
			name:  "Empty stand and desk",
			email: "<EMAIL>",
			mockAgent: dto.Agent{
				ID:      1,
				Email:   "<EMAIL>",
				StandID: sql.NullInt64{Int64: 0, Valid: false},
				Desk:    sql.NullInt64{Int64: 0, Valid: false},
			},
			expectedErr: nil,
			expectedAgent: domain.Agent{
				ID:      1,
				Email:   "<EMAIL>",
				StandID: 0,
				Desk:    0,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetAgentByEmail", tt.email).Return(tt.mockAgent, tt.mockErr)

			// Call the method
			agent, err := client.GetAgentByEmail(tt.email)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedAgent, agent)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestGetStandByID(t *testing.T) {
	tests := []struct {
		name          string
		id            int
		mockStand     dto.Stand
		mockDesks     []int
		mockErr       error
		mockDesksErr  error
		expectedErr   error
		expectedStand domain.Stand
	}{
		{
			name: "Valid stand",
			id:   1,
			mockStand: dto.Stand{
				ID:        1,
				Name:      "Stand1",
				CountryID: 1,
			},
			mockDesks:   []int{1, 2},
			expectedErr: nil,
			expectedStand: domain.Stand{
				ID:            1,
				Name:          "Stand1",
				CountryID:     1,
				SelectedDesks: []int{1, 2},
			},
		},
		{
			name: "Valid stand with no desks",
			id:   1,
			mockStand: dto.Stand{
				ID:        1,
				Name:      "Stand1",
				CountryID: 1,
			},
			mockDesks:   []int{},
			expectedErr: nil,
			expectedStand: domain.Stand{
				ID:            1,
				Name:          "Stand1",
				CountryID:     1,
				SelectedDesks: []int{},
			},
		},
		{
			name:        "Stand not found",
			id:          2,
			mockErr:     sql.ErrNoRows,
			expectedErr: errors.New("stand 2 not found"),
		},
		{
			name:        "Database error",
			id:          3,
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
		{
			name: "Desk query error",
			id:   4,
			mockStand: dto.Stand{
				ID:        4,
				Name:      "Stand4",
				CountryID: 1,
			},
			mockDesksErr: errors.New("desk query error"),
			expectedErr:  errors.New("desk query error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetStandByID", tt.id).Return(tt.mockStand, tt.mockErr)
			if tt.mockErr == nil {
				repo.On("GetStandSelectedDesksByItsAgents", tt.id).Return(tt.mockDesks, tt.mockDesksErr)
			}

			// Call the method
			stand, err := client.GetStandByID(tt.id)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStand, stand)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestGetAgentUIDataByEmail(t *testing.T) {
	tests := []struct {
		name           string
		email          string
		expectedErr    error
		expectedResult domain.AgentUIData
		mocks          func(repo *mockRepo.Repo)
	}{
		{
			name:        "Valid agent with stand and appointments",
			email:       "<EMAIL>",
			expectedErr: nil,
			expectedResult: domain.AgentUIData{
				Agent: domain.Agent{
					ID:      1,
					Email:   "<EMAIL>",
					StandID: 1,
				},
				Stand: domain.Stand{
					ID:            1,
					Name:          "Stand1",
					CountryID:     1,
					SelectedDesks: []int{},
				},
				OpenAppointments: []domain.Appointment{
					{ID: 1, LineID: "ES1O1234", ContactReason: domain.ContactReason{ID: 1, Name: "TestReason"}, CreatedAt: sql.NullTime{}.Time, AgentID: 1, Status: "open", Requester: domain.Requester{ID: 1, Name: "John", Surname: "Doe"}},
				},
			},
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAgentByEmail", "<EMAIL>").Return(dto.Agent{
					ID:      1,
					Email:   "<EMAIL>",
					StandID: sql.NullInt64{Int64: 1, Valid: true},
				}, nil)
				repo.On("GetStandByID", 1).Return(dto.Stand{
					ID:        1,
					Name:      "Stand1",
					CountryID: 1,
				}, nil)
				repo.On("GetCountryByID", 1).Return(dto.Country{
					ID:   1,
					Name: "ARG",
				}, nil)
				repo.On("GetStandDayAppointments", 1, "America/Argentina/Buenos_Aires").Return([]dto.Appointment{
					{ID: 1, LineID: "ES1O1234", ContactReason: 1, CreatedAt: sql.NullTime{Time: sql.NullTime{}.Time, Valid: true}, AgentID: 1, Status: "open", RequesterID: 1},
				}, nil)
				repo.On("GetStandSelectedDesksByItsAgents", 1).Return([]int{}, nil)
				repo.On("GetContactReasonByID", 1).Return(dto.ContactReason{ID: 1, Name: "TestReason"}, nil)
				repo.On("GetRequesterByID", 1).Return(dto.Requester{ID: 1, Name: "John", Surname: "Doe"}, nil)
			},
		},
		{
			name:        "Agent not found",
			email:       "<EMAIL>",
			expectedErr: errors.New("agent <EMAIL> not found"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAgentByEmail", "<EMAIL>").Return(dto.Agent{}, sql.ErrNoRows)
			},
		},
		{
			name:        "Stand not found",
			email:       "<EMAIL>",
			expectedErr: errors.New("stand 1 not found"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAgentByEmail", "<EMAIL>").Return(dto.Agent{
					ID:      1,
					Email:   "<EMAIL>",
					StandID: sql.NullInt64{Int64: 1, Valid: true},
				}, nil)
				repo.On("GetStandByID", 1).Return(dto.Stand{}, sql.ErrNoRows)
			},
		},
		{
			name:        "Country not found",
			email:       "<EMAIL>",
			expectedErr: errors.New("country 1 not found"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAgentByEmail", "<EMAIL>").Return(dto.Agent{
					ID:      1,
					Email:   "<EMAIL>",
					StandID: sql.NullInt64{Int64: 1, Valid: true},
				}, nil)
				repo.On("GetStandByID", 1).Return(dto.Stand{
					ID:        1,
					Name:      "Stand1",
					CountryID: 1,
				}, nil)
				repo.On("GetStandSelectedDesksByItsAgents", 1).Return([]int{}, nil)
				repo.On("GetCountryByID", 1).Return(dto.Country{}, sql.ErrNoRows)
			},
		},
		{
			name:        "Error retrieving appointments",
			email:       "<EMAIL>",
			expectedErr: errors.New("error retrieving appointments"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAgentByEmail", "<EMAIL>").Return(dto.Agent{
					ID:      1,
					Email:   "<EMAIL>",
					StandID: sql.NullInt64{Int64: 1, Valid: true},
				}, nil)
				repo.On("GetStandByID", 1).Return(dto.Stand{
					ID:        1,
					Name:      "Stand1",
					CountryID: 1,
				}, nil)
				repo.On("GetStandSelectedDesksByItsAgents", 1).Return([]int{}, nil)
				repo.On("GetCountryByID", 1).Return(dto.Country{
					ID:   1,
					Name: "ARG",
				}, nil)
				repo.On("GetStandDayAppointments", 1, "America/Argentina/Buenos_Aires").Return(nil, errors.New("error retrieving appointments"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			tt.mocks(repo)

			// Call the method
			result, err := client.GetAgentUIDataByEmail(tt.email)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestSetAgentDeskByEmail(t *testing.T) {
	tests := []struct {
		name        string
		email       string
		desk        int
		mockAgent   dto.Agent
		mockErr     error
		expectedErr error
	}{
		{
			name:  "Valid agent",
			email: "<EMAIL>",
			desk:  5,
			mockAgent: dto.Agent{
				ID:    1,
				Email: "<EMAIL>",
			},
			expectedErr: nil,
		},
		{
			name:        "Agent not found",
			email:       "<EMAIL>",
			desk:        5,
			mockErr:     sql.ErrNoRows,
			expectedErr: errors.New("agent <EMAIL> not found"),
		},
		{
			name:        "Database error",
			email:       "<EMAIL>",
			desk:        5,
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetAgentByEmail", tt.email).Return(tt.mockAgent, tt.mockErr)
			if tt.mockErr == nil {
				repo.On("SetAgentDesk", tt.mockAgent.ID, tt.desk).Return(nil)
			}

			// Call the method
			err := client.SetAgentDeskByEmail(tt.email, tt.desk)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestUpdateAssignAppointment(t *testing.T) {
	timeToAttend := time.Now()
	tests := []struct {
		name            string
		appointment     domain.Appointment
		mockAppointment dto.Appointment
		expectedCall    dto.Appointment
		mockErr         error
		expectedErr     error
	}{
		{
			name: "Valid appointment update",
			appointment: domain.Appointment{
				ID:         1,
				StandID:    2,
				AgentID:    3,
				Status:     domain.AttentionCalled,
				AttendedAt: timeToAttend,
			},
			mockAppointment: dto.Appointment{
				ID:      1,
				StandID: 2,
				AgentID: 0,
				Status:  domain.AttentionCreated.String(),
			},
			expectedCall: dto.Appointment{
				ID:      1,
				StandID: 2,
				AgentID: 3,
				Status:  domain.AttentionCalled.String(),
				AttendedAt: sql.NullTime{
					Time:  timeToAttend,
					Valid: true,
				},
			},
			expectedErr: nil,
		},
		{
			name: "Appointment not found",
			appointment: domain.Appointment{
				ID: 1,
			},
			mockErr:     sql.ErrNoRows,
			expectedErr: fmt.Errorf("appointment %d not found", 1),
		},
		{
			name: "Database error",
			appointment: domain.Appointment{
				ID: 1,
			},
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetAppointmentByID", tt.appointment.ID).Return(tt.mockAppointment, tt.mockErr)
			if tt.mockErr == nil {
				repo.On("SaveAppointment", tt.expectedCall).Return(nil)
			}

			// Call the method
			err := client.AssignAppointment(tt.appointment)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestCancelAppointment(t *testing.T) {
	tests := []struct {
		name        string
		appointment domain.Appointment
		expectedErr error
		mocks       func(repo *mockRepo.Repo)
	}{
		{
			name: "Valid appointment cancellation",
			appointment: domain.Appointment{
				ID: 1,
			},
			expectedErr: nil,
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 1).Return(dto.Appointment{
					ID:     1,
					Status: domain.AttentionCreated.String(),
				}, nil)
				repo.On("SaveAppointment", dto.Appointment{
					ID:     1,
					Status: domain.AttentionCancelled.String(),
				}).Return(nil)
			},
		},
		{
			name: "Appointment not found",
			appointment: domain.Appointment{
				ID: 1,
			},
			expectedErr: fmt.Errorf("appointment %d not found", 1),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 1).Return(dto.Appointment{}, sql.ErrNoRows)
			},
		},
		{
			name: "Database error on GetAppointmentByID",
			appointment: domain.Appointment{
				ID: 1,
			},
			expectedErr: errors.New("database error"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 1).Return(dto.Appointment{}, errors.New("database error"))
			},
		},
		{
			name: "Database error on SaveAppointment",
			appointment: domain.Appointment{
				ID: 1,
			},
			expectedErr: errors.New("database error"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 1).Return(dto.Appointment{
					ID:     1,
					Status: domain.AttentionCreated.String(),
				}, nil)
				repo.On("SaveAppointment", dto.Appointment{
					ID:     1,
					Status: domain.AttentionCancelled.String(),
				}).Return(errors.New("database error"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			tt.mocks(repo)

			// Call the method
			err := client.CancelAppointment(tt.appointment)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestGetAppointmentStatus(t *testing.T) {
	tests := []struct {
		name           string
		id             int
		expectedErr    error
		expectedStatus domain.AttentionStatus
		mocks          func(repo *mockRepo.Repo)
	}{
		{
			name:           "Valid appointment status",
			id:             1,
			expectedStatus: domain.AttentionCreated,
			expectedErr:    nil,
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 1).Return(dto.Appointment{
					ID:     1,
					Status: domain.AttentionCreated.String(),
				}, nil)
			},
		},
		{
			name:        "Appointment not found",
			id:          2,
			expectedErr: fmt.Errorf("appointment %d not found", 2),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 2).Return(dto.Appointment{}, sql.ErrNoRows)
			},
		},
		{
			name:        "Database error",
			id:          3,
			expectedErr: errors.New("database error"),
			mocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByID", 3).Return(dto.Appointment{}, errors.New("database error"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			tt.mocks(repo)

			// Call the method
			status, err := client.GetAppointmentStatus(tt.id)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, status)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestFilterAppointmentsByAgent(t *testing.T) {
	tests := []struct {
		name             string
		openAppointments []domain.Appointment
		agentID          int
		expectedResult   []domain.Appointment
	}{
		{
			name: "No appointments addressed by other agents",
			openAppointments: []domain.Appointment{
				{ID: 1, AgentID: 0},
				{ID: 2, AgentID: 0},
			},
			agentID: 1,
			expectedResult: []domain.Appointment{
				{ID: 1, AgentID: 0},
				{ID: 2, AgentID: 0},
			},
		},
		{
			name: "Some appointments addressed by other agents",
			openAppointments: []domain.Appointment{
				{ID: 1, AgentID: 1},
				{ID: 2, AgentID: 2},
				{ID: 3, AgentID: 0},
			},
			agentID: 1,
			expectedResult: []domain.Appointment{
				{ID: 1, AgentID: 1},
				{ID: 3, AgentID: 0},
			},
		},
		{
			name: "All appointments addressed by other agents",
			openAppointments: []domain.Appointment{
				{ID: 1, AgentID: 2},
				{ID: 2, AgentID: 3},
			},
			agentID:        1,
			expectedResult: []domain.Appointment{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := filterUnassignedOrOwnAppointments(tt.openAppointments, tt.agentID)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestChangeStatusByZendeskID(t *testing.T) {
	tests := []struct {
		name            string
		zendeskID       int
		status          domain.AttentionStatus
		mockAppointment dto.Appointment
		mockErr         error
		expectedErr     error
		expected        domain.Appointment
	}{
		{
			name:      "Valid status change",
			zendeskID: 1,
			status:    domain.AttentionClosed,
			mockAppointment: dto.Appointment{
				ID:     1,
				Status: domain.AttentionCreated.String(),
			},
			expectedErr: nil,
			expected:    domain.Appointment{ID: 1, Status: domain.AttentionClosed},
		},
		{
			name:        "Appointment not found",
			zendeskID:   2,
			status:      domain.AttentionClosed,
			mockErr:     sql.ErrNoRows,
			expectedErr: fmt.Errorf("appointment with zendeskID %d not found", 2),
			expected:    domain.Appointment{},
		},
		{
			name:        "Database error",
			zendeskID:   3,
			status:      domain.AttentionClosed,
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
			expected:    domain.Appointment{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetAppointmentByZendeskID", tt.zendeskID).Return(tt.mockAppointment, tt.mockErr)
			if tt.mockErr == nil {
				repo.On("SaveAppointment", mock.MatchedBy(func(app dto.Appointment) bool {
					return app.Status == tt.status.String()
				})).Return(nil)
			}

			// Call the method
			appointment, err := client.ChangeStatusByZendeskID(tt.zendeskID, tt.status)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, appointment)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestGetLineDataByStandName(t *testing.T) {
	tests := []struct {
		name           string
		standName      string
		mockStand      dto.Stand
		mockStandErr   error
		mockLineData   domain.LineData
		mockLineErr    error
		expectedErr    error
		expectedResult domain.LineData
	}{
		{
			name:      "Valid stand",
			standName: "Stand1",
			mockStand: dto.Stand{
				ID:        1,
				Name:      "Stand1",
				CountryID: 1,
			},
			mockLineData: domain.LineData{
				Stand: domain.Stand{ID: 1, Name: "Stand1"},
				WorkingAgents: []domain.Agent{
					{ID: 1, Email: "<EMAIL>", StandID: 1},
				},
			},
			expectedErr: nil,
			expectedResult: domain.LineData{
				Stand: domain.Stand{ID: 1, Name: "Stand1"},
				WorkingAgents: []domain.Agent{
					{ID: 1, Email: "<EMAIL>", StandID: 1},
				},
			},
		},
		{
			name:         "Stand not found",
			standName:    "NonExistentStand",
			mockStandErr: sql.ErrNoRows,
			expectedErr:  fmt.Errorf("stand NonExistentStand not found"),
		},
		{
			name:         "Database error",
			standName:    "ErrorStand",
			mockStandErr: errors.New("database error"),
			expectedErr:  errors.New("database error"),
		},
		{
			name:      "Error retrieving line data",
			standName: "StandWithError",
			mockStand: dto.Stand{
				ID:        2,
				Name:      "StandWithError",
				CountryID: 1,
			},
			mockLineErr: errors.New("error retrieving line data"),
			expectedErr: errors.New("error retrieving line data"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			repo.On("GetStandByName", tt.standName).Return(tt.mockStand, tt.mockStandErr)

			if tt.mockStandErr == nil {
				if tt.mockLineErr != nil {
					repo.On("GetWorkingAgentsByStandID", tt.mockStand.ID).Return(nil, tt.mockLineErr)
				} else {
					repo.On("GetWorkingAgentsByStandID", tt.mockStand.ID).Return([]dto.Agent{
						{ID: 1, Email: "<EMAIL>", StandID: sql.NullInt64{Int64: 1, Valid: true}, Desk: sql.NullInt64{Int64: 0, Valid: false}},
					}, nil)
					repo.On("GetStandByID", tt.mockStand.ID).Return(tt.mockStand, nil)
					repo.On("GetStandSelectedDesksByItsAgents", tt.mockStand.ID).Return([]int{}, nil)
					repo.On("GetCountryByID", tt.mockStand.CountryID).Return(dto.Country{ID: 1, Name: "ESP"}, nil)
					repo.On("GetStandDayAppointments", tt.mockStand.ID, mock.AnythingOfType("string")).Return([]dto.Appointment{}, nil)
				}
			}

			result, err := client.GetLineDataByStandName(tt.standName)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestGetAllStandNames(t *testing.T) {
	tests := []struct {
		name          string
		mockStands    []dto.Stand
		mockErr       error
		expectedNames []string
		expectedErr   error
	}{
		{
			name: "Multiple stands found",
			mockStands: []dto.Stand{
				{ID: 1, Name: "Stand1", CountryID: 1},
				{ID: 2, Name: "Stand2", CountryID: 1},
				{ID: 3, Name: "Stand3", CountryID: 2},
			},
			mockErr:       nil,
			expectedNames: []string{"Stand1", "Stand2", "Stand3"},
			expectedErr:   nil,
		},
		{
			name:          "No stands found",
			mockStands:    []dto.Stand{},
			mockErr:       nil,
			expectedNames: nil,
			expectedErr:   fmt.Errorf("no stands found"),
		},
		{
			name:          "Database error",
			mockStands:    nil,
			mockErr:       errors.New("database error"),
			expectedNames: nil,
			expectedErr:   errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			repo.On("GetAllStands").Return(tt.mockStands, tt.mockErr)

			names, err := client.GetAllStandNames()

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedNames, names)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestGetContactReasonByName(t *testing.T) {
	tests := []struct {
		name                  string
		contactReasonName     string
		mockContactReason     dto.ContactReason
		mockErr               error
		expectedErr           error
		expectedContactReason domain.ContactReason
	}{
		{
			name:              "Valid contact reason",
			contactReasonName: "Technical Support",
			mockContactReason: dto.ContactReason{
				ID:        1,
				Name:      "Technical Support",
				Audience:  "Customers",
				Kind:      "Standard",
				Detail:    sql.NullString{String: "Help with technical issues", Valid: true},
				InputTag:  sql.NullString{String: "tech_support", Valid: true},
				OutputTag: sql.NullString{String: "resolved", Valid: true},
				Solution:  sql.NullString{String: "Provide troubleshooting steps", Valid: true},
				Category:  sql.NullString{String: "Technical", Valid: true},
			},
			expectedErr: nil,
			expectedContactReason: domain.ContactReason{
				ID:        1,
				Name:      "Technical Support",
				Audience:  "Customers",
				Kind:      "Standard",
				Detail:    "Help with technical issues",
				InputTag:  "tech_support",
				OutputTag: "resolved",
				Solution:  "Provide troubleshooting steps",
				Category:  "Technical",
			},
		},
		{
			name:                  "Contact reason not found",
			contactReasonName:     "Non Existent Reason",
			mockErr:               sql.ErrNoRows,
			expectedErr:           errors.New("contact reason Non Existent Reason not found"),
			expectedContactReason: domain.ContactReason{},
		},
		{
			name:                  "Database error",
			contactReasonName:     "Technical Support",
			mockErr:               errors.New("database error"),
			expectedErr:           errors.New("database error"),
			expectedContactReason: domain.ContactReason{},
		},
		{
			name:              "Contact reason with null fields",
			contactReasonName: "Basic Support",
			mockContactReason: dto.ContactReason{
				ID:        2,
				Name:      "Basic Support",
				Audience:  "Customers",
				Kind:      "Standard",
				Detail:    sql.NullString{Valid: false},
				InputTag:  sql.NullString{Valid: false},
				OutputTag: sql.NullString{Valid: false},
				Solution:  sql.NullString{Valid: false},
				Category:  sql.NullString{Valid: false},
			},
			expectedErr: nil,
			expectedContactReason: domain.ContactReason{
				ID:        2,
				Name:      "Basic Support",
				Audience:  "Customers",
				Kind:      "Standard",
				Detail:    "",
				InputTag:  "",
				OutputTag: "",
				Solution:  "",
				Category:  "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			client := &Client{repo: repo}

			// Set up mock expectations
			repo.On("GetContactReasonByName", tt.contactReasonName).Return(tt.mockContactReason, tt.mockErr)

			// Call the method
			result, err := client.GetContactReasonByName(tt.contactReasonName)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedContactReason, result)
			}

			// Assert expectations
			repo.AssertExpectations(t)
		})
	}
}

func TestGetAppointmentByPartnerID(t *testing.T) {
	tests := []struct {
		name          string
		partnerID     int
		setupMocks    func(*mockRepo.Repo)
		expectedError error
		expectedID    int
	}{
		{
			name:      "successful retrieval",
			partnerID: 123,
			setupMocks: func(repo *mockRepo.Repo) {
				appointment := dto.Appointment{
					ID:            1,
					LineID:        "R-TURN123",
					ContactReason: 5,
					StandID:       2,
					AgentID:       3,
					Status:        domain.AttentionCreated.String(),
					ZTicketID:     sql.NullInt64{Int64: 456, Valid: true},
				}
				contactReason := dto.ContactReason{
					ID:       5,
					Name:     "Test Contact Reason",
					Audience: "Test Audience",
					Kind:     "Test Kind",
				}
				repo.On("GetAppointmentByPartnerID", 123).Return(appointment, nil)
				repo.On("GetContactReasonByID", 5).Return(contactReason, nil)
			},
			expectedError: nil,
			expectedID:    1,
		},
		{
			name:      "appointment not found",
			partnerID: 999,
			setupMocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByPartnerID", 999).Return(dto.Appointment{}, sql.ErrNoRows)
			},
			expectedError: fmt.Errorf("appointment with partnerID 999 not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			tt.setupMocks(repo)

			client := &Client{repo: repo}

			appointment, err := client.GetAppointmentByPartnerID(tt.partnerID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, appointment.ID)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestUpdateAcuityAppointment(t *testing.T) {
	tests := []struct {
		name               string
		partnerID          int
		newPlannedAt       time.Time
		newContactReasonID int
		setupMocks         func(*mockRepo.Repo)
		expectedError      error
	}{
		{
			name:               "successful update",
			partnerID:          123,
			newPlannedAt:       time.Date(2024, 6, 15, 14, 30, 0, 0, time.UTC),
			newContactReasonID: 7,
			setupMocks: func(repo *mockRepo.Repo) {
				appointment := dto.Appointment{
					ID:            1,
					LineID:        "R-TURN123",
					ContactReason: 5,
					StandID:       2,
					AgentID:       3,
					Status:        domain.AttentionCreated.String(),
					ZTicketID:     sql.NullInt64{Int64: 456, Valid: true},
					PlannedAt:     sql.NullTime{Time: time.Date(2024, 6, 10, 10, 0, 0, 0, time.UTC), Valid: true},
				}
				updatedAppointment := appointment
				updatedAppointment.PlannedAt = sql.NullTime{Time: time.Date(2024, 6, 15, 14, 30, 0, 0, time.UTC), Valid: true}
				updatedAppointment.ContactReason = 7

				repo.On("GetAppointmentByPartnerID", 123).Return(appointment, nil)
				repo.On("SaveAppointment", updatedAppointment).Return(nil)
			},
			expectedError: nil,
		},
		{
			name:               "appointment not found",
			partnerID:          999,
			newPlannedAt:       time.Date(2024, 6, 15, 14, 30, 0, 0, time.UTC),
			newContactReasonID: 7,
			setupMocks: func(repo *mockRepo.Repo) {
				repo.On("GetAppointmentByPartnerID", 999).Return(dto.Appointment{}, sql.ErrNoRows)
			},
			expectedError: fmt.Errorf("appointment with partner ID 999 not found"),
		},
		{
			name:               "database error on save",
			partnerID:          123,
			newPlannedAt:       time.Date(2024, 6, 15, 14, 30, 0, 0, time.UTC),
			newContactReasonID: 7,
			setupMocks: func(repo *mockRepo.Repo) {
				appointment := dto.Appointment{
					ID:            1,
					LineID:        "R-TURN123",
					ContactReason: 5,
					StandID:       2,
					AgentID:       3,
					Status:        domain.AttentionCreated.String(),
					ZTicketID:     sql.NullInt64{Int64: 456, Valid: true},
					PlannedAt:     sql.NullTime{Time: time.Date(2024, 6, 10, 10, 0, 0, 0, time.UTC), Valid: true},
				}
				updatedAppointment := appointment
				updatedAppointment.PlannedAt = sql.NullTime{Time: time.Date(2024, 6, 15, 14, 30, 0, 0, time.UTC), Valid: true}
				updatedAppointment.ContactReason = 7

				repo.On("GetAppointmentByPartnerID", 123).Return(appointment, nil)
				repo.On("SaveAppointment", updatedAppointment).Return(fmt.Errorf("database error"))
			},
			expectedError: fmt.Errorf("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mockRepo.Repo)
			tt.setupMocks(repo)

			client := &Client{repo: repo}

			err := client.UpdateAcuityAppointment(tt.partnerID, tt.newPlannedAt, tt.newContactReasonID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}
