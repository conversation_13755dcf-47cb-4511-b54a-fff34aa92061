package database

import (
	"database/sql"
	"errors"
	"testing"
	"turnero/internal/domain"
	"turnero/internal/infra/database/mocks"
	"turnero/internal/infra/dto"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Agent CRUD Tests

func TestClient_GetAllAgents(t *testing.T) {
	tests := []struct {
		name           string
		mockAgents     []dto.Agent
		mockErr        error
		expectedAgents []domain.Agent
		expectedErr    error
	}{
		{
			name: "Success - multiple agents",
			mockAgents: []dto.Agent{
				{
					ID:        1,
					Email:     "<EMAIL>",
					StandID:   sql.NullInt64{Int64: 1, Valid: true},
					IsWorking: false,
					Desk:      sql.NullInt64{Int64: 5, Valid: true},
				},
				{
					ID:        2,
					Email:     "<EMAIL>",
					StandID:   sql.NullInt64{Int64: 2, Valid: true},
					IsWorking: true,
					Desk:      sql.NullInt64{Int64: 10, Valid: true},
				},
			},
			expectedAgents: []domain.Agent{
				{ID: 1, Email: "<EMAIL>", StandID: 1, IsWorking: false, Desk: 5},
				{ID: 2, Email: "<EMAIL>", StandID: 2, IsWorking: true, Desk: 10},
			},
			expectedErr: nil,
		},
		{
			name:           "Success - empty list",
			mockAgents:     []dto.Agent{},
			expectedAgents: []domain.Agent{},
			expectedErr:    nil,
		},
		{
			name:        "Database error",
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("GetAllAgents").Return(tt.mockAgents, tt.mockErr)

			agents, err := client.GetAllAgents()

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedAgents, agents)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_CreateAgent(t *testing.T) {
	tests := []struct {
		name        string
		agent       domain.Agent
		mockID      int
		mockErr     error
		expectedID  int
		expectedErr error
	}{
		{
			name: "Success",
			agent: domain.Agent{
				Email:     "<EMAIL>",
				StandID:   1,
				IsWorking: false,
				Desk:      5,
			},
			mockID:     1,
			expectedID: 1,
		},
		{
			name: "Database error",
			agent: domain.Agent{
				Email: "<EMAIL>",
			},
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("CreateAgent", mock.MatchedBy(func(agent dto.Agent) bool {
				return agent.Email == tt.agent.Email
			})).Return(tt.mockID, tt.mockErr)

			id, err := client.CreateAgent(tt.agent)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, id)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_UpdateAgent(t *testing.T) {
	tests := []struct {
		name        string
		id          int
		agent       domain.Agent
		mockErr     error
		expectedErr error
	}{
		{
			name: "Success",
			id:   1,
			agent: domain.Agent{
				Email:     "<EMAIL>",
				StandID:   2,
				IsWorking: true,
				Desk:      10,
			},
		},
		{
			name:        "Database error",
			id:          1,
			agent:       domain.Agent{Email: "<EMAIL>"},
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("UpdateAgent", tt.id, mock.MatchedBy(func(agent dto.Agent) bool {
				return agent.Email == tt.agent.Email
			})).Return(tt.mockErr)

			err := client.UpdateAgent(tt.id, tt.agent)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_DeleteAgent(t *testing.T) {
	tests := []struct {
		name        string
		id          int
		mockErr     error
		expectedErr error
	}{
		{
			name: "Success",
			id:   1,
		},
		{
			name:        "Database error",
			id:          1,
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("DeleteAgent", tt.id).Return(tt.mockErr)

			err := client.DeleteAgent(tt.id)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}

// Stand CRUD Tests

func TestClient_CreateStand(t *testing.T) {
	tests := []struct {
		name        string
		stand       domain.Stand
		mockID      int
		mockErr     error
		expectedID  int
		expectedErr error
	}{
		{
			name: "Success",
			stand: domain.Stand{
				Name:      "New Office",
				CountryID: 1,
			},
			mockID:     1,
			expectedID: 1,
		},
		{
			name: "Database error",
			stand: domain.Stand{
				Name: "Error Office",
			},
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("CreateStand", mock.MatchedBy(func(stand dto.Stand) bool {
				return stand.Name == tt.stand.Name
			})).Return(tt.mockID, tt.mockErr)

			id, err := client.CreateStand(tt.stand)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, id)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_UpdateStand(t *testing.T) {
	tests := []struct {
		name        string
		id          int
		stand       domain.Stand
		mockErr     error
		expectedErr error
	}{
		{
			name: "Success",
			id:   1,
			stand: domain.Stand{
				Name:      "Updated Office",
				CountryID: 2,
			},
		},
		{
			name:        "Database error",
			id:          1,
			stand:       domain.Stand{Name: "Error Office"},
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("UpdateStand", tt.id, mock.MatchedBy(func(stand dto.Stand) bool {
				return stand.Name == tt.stand.Name
			})).Return(tt.mockErr)

			err := client.UpdateStand(tt.id, tt.stand)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_DeleteStand(t *testing.T) {
	tests := []struct {
		name        string
		id          int
		mockErr     error
		expectedErr error
	}{
		{
			name: "Success",
			id:   1,
		},
		{
			name:        "Database error",
			id:          1,
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("DeleteStand", tt.id).Return(tt.mockErr)

			err := client.DeleteStand(tt.id)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}

// Contact Reason CRUD Tests

func TestClient_CreateContactReason(t *testing.T) {
	tests := []struct {
		name          string
		contactReason domain.ContactReason
		mockID        int
		mockErr       error
		expectedID    int
		expectedErr   error
	}{
		{
			name: "Success",
			contactReason: domain.ContactReason{
				Name:     "New Support Type",
				Audience: "customer",
				Kind:     "support",
				Detail:   "Detailed description",
			},
			mockID:     1,
			expectedID: 1,
		},
		{
			name: "Database error",
			contactReason: domain.ContactReason{
				Name: "Error Type",
			},
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("CreateContactReason", mock.MatchedBy(func(cr dto.ContactReason) bool {
				return cr.Name == tt.contactReason.Name
			})).Return(tt.mockID, tt.mockErr)

			id, err := client.CreateContactReason(tt.contactReason)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, id)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_UpdateContactReason(t *testing.T) {
	tests := []struct {
		name          string
		id            int
		contactReason domain.ContactReason
		mockErr       error
		expectedErr   error
	}{
		{
			name: "Success",
			id:   1,
			contactReason: domain.ContactReason{
				Name:     "Updated Support Type",
				Audience: "customer",
				Kind:     "support",
			},
		},
		{
			name:          "Database error",
			id:            1,
			contactReason: domain.ContactReason{Name: "Error Type"},
			mockErr:       errors.New("database error"),
			expectedErr:   errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("UpdateContactReason", tt.id, mock.MatchedBy(func(cr dto.ContactReason) bool {
				return cr.Name == tt.contactReason.Name
			})).Return(tt.mockErr)

			err := client.UpdateContactReason(tt.id, tt.contactReason)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_DeleteContactReason(t *testing.T) {
	tests := []struct {
		name        string
		id          int
		mockErr     error
		expectedErr error
	}{
		{
			name: "Success",
			id:   1,
		},
		{
			name:        "Database error",
			id:          1,
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("DeleteContactReason", tt.id).Return(tt.mockErr)

			err := client.DeleteContactReason(tt.id)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}

			repo.AssertExpectations(t)
		})
	}
}

func TestClient_GetAllContactReasons(t *testing.T) {
	tests := []struct {
		name                   string
		mockContactReasons     []dto.ContactReason
		mockErr                error
		expectedContactReasons []domain.ContactReason
		expectedErr            error
	}{
		{
			name: "Success - multiple contact reasons",
			mockContactReasons: []dto.ContactReason{
				{
					ID:       1,
					Name:     "Technical Support",
					Audience: "customer",
					Kind:     "support",
					Detail:   sql.NullString{String: "Technical assistance", Valid: true},
				},
				{
					ID:       2,
					Name:     "Sales Inquiry",
					Audience: "prospect",
					Kind:     "sales",
					Detail:   sql.NullString{String: "", Valid: false},
				},
			},
			expectedContactReasons: []domain.ContactReason{
				{ID: 1, Name: "Technical Support", Audience: "customer", Kind: "support", Detail: "Technical assistance"},
				{ID: 2, Name: "Sales Inquiry", Audience: "prospect", Kind: "sales", Detail: ""},
			},
			expectedErr: nil,
		},
		{
			name:                   "Success - empty list",
			mockContactReasons:     []dto.ContactReason{},
			expectedContactReasons: []domain.ContactReason{},
			expectedErr:            nil,
		},
		{
			name:        "Database error",
			mockErr:     errors.New("database error"),
			expectedErr: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := new(mocks.Repo)
			client := &Client{repo: repo}

			repo.On("GetAllContactReasons").Return(tt.mockContactReasons, tt.mockErr)

			contactReasons, err := client.GetAllContactReasons()

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.EqualError(t, err, tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedContactReasons, contactReasons)
			}

			repo.AssertExpectations(t)
		})
	}
}
