package domain

import "time"

type Appointment struct {
	ID              int             `json:"id"`
	LineID          string          `json:"line_id"`
	Requester       Requester       `json:"requester"`
	ContactReason   ContactReason   `json:"contact_reason"`
	CreatedAt       time.Time       `json:"created_at"`
	PlannedAt       time.Time       `json:"planned_at"`
	ZendeskTicketID int             `json:"zendesk_ticket_id"`
	StandID         int             `json:"stand_id"`
	StandDesk       int             `json:"stand_desk"`
	AgentID         int             `json:"agent_id"`
	Status          AttentionStatus `json:"status"`
	LastDO          time.Time       `json:"last_do"`
	AttendedAt      time.Time       `json:"attended_at"`
	Origin          string          `json:"origin"`
}

type AttentionStatus string

const (
	AttentionUndefined   AttentionStatus = ""
	AttentionCreated     AttentionStatus = "created"
	AttentionReadyToCall AttentionStatus = "ready_to_call"
	AttentionCalled      AttentionStatus = "called"
	AttentionClosed      AttentionStatus = "closed"
	AttentionCancelled   AttentionStatus = "cancelled"
)

func (a AttentionStatus) String() string {
	return string(a)
}
