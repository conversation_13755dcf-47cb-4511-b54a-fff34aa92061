package domain

import "time"

type PartnerAppointment struct {
	ID            int       `json:"id"`
	Partner       string    `json:"partner"`
	Name          string    `json:"name"`
	Surname       string    `json:"surname"`
	Phone         string    `json:"phone"`
	Email         string    `json:"email"`
	Stand         string    `json:"stand"`
	ContactReason string    `json:"contact_reason"`
	CreatedAt     time.Time `json:"created_at"`
	PlannedAt     time.Time `json:"planned_at"`
	Status        string    `json:"status"`
}
