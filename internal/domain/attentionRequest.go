package domain

import "time"

const (
	AttentionKindSupport    = "S"
	AttentionKindOnboarding = "O"
	AttentionLineDigits     = 4
)

type AttentionRequest struct {
	Requester     Requester
	ContactReason ContactReason
	Stand         Stand
	LineID        string
	TicketID      int
	CountryName   string
	PlannedAt     time.Time // For Acuity appointments, this is the scheduled time
	PartnerID     int       // For Acuity appointments, this is the Acuity appointment ID
	Origin        string    // "tablet" or "acuity"
}
