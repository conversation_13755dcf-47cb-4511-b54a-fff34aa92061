package domain

const LineEmail = "<h4><PERSON><PERSON></h4><p><PERSON>rac<PERSON> %s por solicitar atención en nuestro stand %s por el motivo %s. Su turno es el:</p> <h1>%s</h1>"
const LineEmailSubject = "Turnero - Número de atención"
const ErrorGeneratingLineAttention = "Error generando turno, intente nuevamente"
const ErrorOnContactReason = "Razón de contacto no encontrada o inválida"
const ErrorAssigningAttention = "Error asignando turno, el turno ya fue asignado a otro Agente"
const ErrorNotValidDesk = "No hay mesa seleccionada"
const ErrorCancelingAttention = "Error cancelando turno, vuelva a intentarlo o hágalo manualmente en Zendesk"
const AttentionStatusNotValidToCall = "attention status not valid to call"

type ZendeskTicketStatus string

const (
	Open    ZendeskTicketStatus = "open"
	Solved  ZendeskTicketStatus = "solved"
	New     ZendeskTicketStatus = "new"
	Pending ZendeskTicketStatus = "pending"
	Hold    ZendeskTicketStatus = "hold"
	Closed  ZendeskTicketStatus = "closed"
)

func (z ZendeskTicketStatus) String() string {
	return string(z)
}

type ZendeskTag string

const (
	RequesterNoShow   ZendeskTag = "tag_500102"
	RequesterCanceled ZendeskTag = "requester_canceled"
)

func (z ZendeskTag) String() string {
	return string(z)
}
