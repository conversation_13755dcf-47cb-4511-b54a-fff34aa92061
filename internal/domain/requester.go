package domain

import "time"

type Requester struct {
	ID              int       `json:"id"`
	UserID          string    `json:"user_id"` // This is the user_id from the Admin
	Email           string    `json:"email"`
	Name            string    `json:"name"`
	Surname         string    `json:"surname"`
	ZendeskID       int       `json:"zendesk_id"`
	LastDOOnRequest time.Time `json:"last_do_on_request"`
	Segment         string    `json:"segment"`
}
