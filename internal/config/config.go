package config

import (
	_ "embed"
	"fmt"
	"os"

	"gopkg.in/yaml.v3"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
)

//go:embed zendeskEnvs.yml
var zendeskEnvs []byte

type Config struct {
	BaseURL                      string `default:""`
	AdminURL                     string `default:"https://cabify.com/admin"`
	DB                           *MysqlConfig
	Populate                     bool   `default:"false"`
	PopulateFile                 string `default:"data/developmentData.json"`
	FeatureStore                 string `default:"http://feature-store.feature-store/features/"`
	UsersConnection              string `default:"users-grpc.users:80"`
	Mailgun                      *MailgunConfig
	CustomFields                 *ZendeskCustomFields
	ZendeskIncomingRequestsToken string `default:""`
	Acuity                       *AcuityConfig
}

type MysqlConfig struct {
	User          string `default:"root"`
	Password      string `default:"secret"`
	Database      string `default:"turnero"`
	Host          string `default:"localhost"`
	Port          string `default:"3306"`
	Options       string `default:"tls=skip-verify&parseTime=true&multiStatements=true"`
	MigrationsDir string `default:"migrations"`
}

type MailgunConfig struct {
	Username      string
	Password      string
	Server        string `default:"smtp.eu.mailgun.org"`
	Port          string `default:"587"`
	Headers       map[string]string
	RequesterName string `default:"Turnero"`
	From          string `default:"<EMAIL>"` // TODO ADD THIS PRODUCTION ENV
}

var headers = map[string]string{
	"Content-Type":  "text/html; charset=UTF-8",
	"MIME-Version":  "1.0",
	"X-Mailgun-Tag": "turnero",
	"Reply-to":      "no-reply",
}

type ZendeskCustomFields struct {
	ContactReason int    `default:"8526266630290" yaml:"contact_reason"` // Motivo de contacto Production ID = 360008721299
	Country       int    `default:"8526230955026" yaml:"country"`        // Pais Production ID = 26458969
	InputTag      int    `default:"8526264126738" yaml:"input_tag"`      // Production = 360005302119
	Acquisition   string `default:"2-61" yaml:"acquisition"`
	Attention     string `default:"2-62" yaml:"attention"`
}

type AcuityConfig struct {
	APIUrl        string `default:""`
	WebhookSecret string `default:""`
	UserID        string `default:""`
	APIKey        string `default:""`
	Enabled       bool   // Calculated dynamically based on required fields
}

type envYAML struct {
	Environments map[string]ZendeskCustomFields `yaml:"environments"`
}

func GetConfig() (*Config, error) {
	_ = godotenv.Load()

	var conf Config
	if err := envconfig.Process("turnero", &conf); err != nil {
		return &conf, fmt.Errorf("error loading env vars: %s", err)
	}

	conf.Mailgun.Headers = headers

	//Load YAML config with all zendesk settings
	var envs envYAML
	if err := yaml.Unmarshal(zendeskEnvs, &envs); err != nil {
		return &conf, fmt.Errorf("error parsing yaml config: %s", err)
	}

	currentEnv := os.Getenv("SERVANT_ENVIRONMENT")
	//Get zendesk custom fields for the current environment or fall back to default
	if currentEnvSettings, ok := envs.Environments[currentEnv]; ok {
		conf.CustomFields = &currentEnvSettings
	}

	// Configure Acuity integration
	conf.Acuity = configureAcuityIntegration()

	return &conf, nil
}

// configureAcuityIntegration sets up Acuity configuration and determines if integration should be enabled
func configureAcuityIntegration() *AcuityConfig {
	acuityConfig := &AcuityConfig{
		APIUrl:        os.Getenv("TURNERO_ACUITY_APIURL"),
		WebhookSecret: os.Getenv("TURNERO_ACUITY_WEBHOOKSECRET"),
		UserID:        os.Getenv("TURNERO_ACUITY_USERID"),
		APIKey:        os.Getenv("TURNERO_ACUITY_APIKEY"),
	}

	// Enable integration only if all required fields are configured
	acuityConfig.Enabled = acuityConfig.APIUrl != "" &&
		acuityConfig.WebhookSecret != "" &&
		acuityConfig.UserID != "" &&
		acuityConfig.APIKey != ""

	return acuityConfig
}
