# Turnero Environment Variables Example

# Database Configuration
TURNERO_DB_USER=root
TURNERO_DB_PASSWORD=secret
TURNERO_DB_DATABASE=turnero
TURNERO_DB_HOST=localhost
TURNERO_DB_PORT=3306
TURNERO_DB_OPTIONS=tls=skip-verify&parseTime=true&multiStatements=true
TURNERO_DB_MIGRATIONSDIR=migrations

# Application Configuration
TURNERO_BASEURL=http://localhost:8080
TURNERO_ADMINURL=https://cabify.com/admin
TURNERO_POPULATE=false
TURNERO_POPULATEFILE=data/developmentData.json

# External Services
TURNERO_FEATURESTORE=http://feature-store.feature-store/features/
TURNERO_USERSCONNECTION=users-grpc.users:80

# Mailgun Configuration
TURNERO_MAILGUN_USERNAME=
TURNERO_MAILGUN_PASSWORD=
TURNERO_MAILGUN_SERVER=smtp.eu.mailgun.org
TURNERO_MAILGUN_PORT=587
TURNERO_MAILGUN_REQUESTERNAME=Turnero
TURNERO_MAILGUN_FROM=<EMAIL>

# Zendesk Configuration
TURNERO_ZENDESKINCOMINGQUESTSTOKEN=your-zendesk-token-here
ZDGO_ZENDESKURL=https://your-zendesk-domain.zendesk.com

# Acuity Scheduling Integration
# All fields are required for integration to be enabled
# WEBHOOKSECRET is used for HMAC-SHA256 signature validation of incoming webhooks
TURNERO_ACUITY_APIURL=https://acuityscheduling.com/api/v1
TURNERO_ACUITY_WEBHOOKSECRET=your-webhook-secret-here
TURNERO_ACUITY_USERID=your-acuity-user-id
TURNERO_ACUITY_APIKEY=your-acuity-api-key

# Environment
SERVANT_ENVIRONMENT=development
