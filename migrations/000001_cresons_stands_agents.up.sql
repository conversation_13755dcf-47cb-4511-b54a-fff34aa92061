CREATE TABLE IF NOT EXISTS countries (
                                         id INT AUTO_INCREMENT PRIMARY KEY,
                                         name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS contact_reasons (
                                 id INT AUTO_INCREMENT PRIMARY KEY,
                                 name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
                                 audience VARCHAR(255) NOT NULL,
                                 kind VARCHAR(255) NOT NULL,
                                 detail TEXT,
                                 input_tag VARCHAR(255),
                                 output_tag VARCHAR(255),
                                 solution TEXT
);

CREATE TABLE IF NOT EXISTS contact_reasons_countries (
                                           contact_reason_id INT,
                                           country_id INT,
                                           PRIMARY KEY (contact_reason_id, country_id),
                                           FOREIGN KEY (contact_reason_id) REFERENCES contact_reasons(id) ON DELETE CASCADE,
                                           FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS stands (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
                        country_id INT NOT NULL,
                        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS agents (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        email VARCHAR(255) NOT NULL UNIQUE,
                        stand_id INT,
                        FOREIGN KEY (stand_id) REFERENCES stands(id) ON DELETE SET NULL
);
