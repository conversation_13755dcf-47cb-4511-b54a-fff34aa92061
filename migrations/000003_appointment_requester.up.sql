CREATE TABLE IF NOT EXISTS requesters (
                                          id INT PRIMARY KEY AUTO_INCREMENT,
                                          user_id VARCHAR(255), -- This is the user_id from the auth service, could be null
                                          name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
                                          surname VA<PERSON>HAR(100) NOT NULL,
                                          email VARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS appointments (
                                            id INT PRIMARY KEY AUTO_INCREMENT,
                                            requester_id INT NOT NULL,
                                            line_id varchar(100) NOT NULL,
                                            contact_reason INT NOT NULL,
                                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                            attended_at TIMESTAMP,
                                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                            zticket_id INT NOT NULL,
                                            comments TEXT,
                                            stand_id INT NOT NULL,
                                            agent_id INT,
                                            status ENUM('created', 'ready_to_call', 'called', 'closed', 'cancelled') NOT NULL,
                                            last_do <PERSON><PERSON><PERSON><PERSON><PERSON>,
                                            <PERSON>OR<PERSON><PERSON><PERSON>Y (requester_id) REFERENCES requesters(id),
                                            FOREI<PERSON><PERSON> (contact_reason) REFERENCES contact_reasons(id),
                                            FOREIGN KEY (stand_id) REFERENCES stands(id)
);