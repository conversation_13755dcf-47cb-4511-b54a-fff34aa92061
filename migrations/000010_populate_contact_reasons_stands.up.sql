-- Populate contact_reasons_stands table with all existing combinations
-- This ensures that all current stands have access to all current contact reasons
-- maintaining the existing behavior after the new relationship is implemented

INSERT INTO contact_reasons_stands (contact_reason_id, stand_id, is_deleted)
SELECT 
    cr.id as contact_reason_id,
    s.id as stand_id,
    FALSE as is_deleted
FROM contact_reasons cr
CROSS JOIN stands s
WHERE cr.is_deleted = FALSE 
  AND s.is_deleted = FALSE
  AND NOT EXISTS (
    -- Avoid duplicates if this migration is run multiple times
    SELECT 1 FROM contact_reasons_stands crs 
    WHERE crs.contact_reason_id = cr.id 
      AND crs.stand_id = s.id
  );
