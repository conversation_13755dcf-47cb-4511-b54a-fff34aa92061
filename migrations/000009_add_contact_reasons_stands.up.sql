-- Create contact_reasons_stands table for direct relationship between stands and contact reasons
CREATE TABLE IF NOT EXISTS contact_reasons_stands (
    contact_reason_id INT,
    stand_id INT,
    is_deleted BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (contact_reason_id, stand_id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (contact_reason_id) REFERENCES contact_reasons(id),
    FOREIGN KEY (stand_id) REFERENCES stands(id)
);
