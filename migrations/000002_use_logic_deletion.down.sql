-- Remove logic deletion columns
ALTER TABLE agents DROP COLUMN is_deleted;
ALTER TABLE stands DROP COLUMN is_deleted;
ALTER TABLE contact_reasons_countries DROP COLUMN is_deleted;
ALTER TABLE contact_reasons DROP COLUMN is_deleted;
ALTER TABLE countries DROP COLUMN is_deleted;

-- Remove the modified foreign keys
ALTER TABLE agents DROP FOREIGN KEY agents_ibfk_1;
ALTER TABLE stands DROP FOREIGN KEY stands_ibfk_1;
ALTER TABLE contact_reasons_countries DROP FOREIGN KEY contact_reasons_countries_ibfk_1;
ALTER TABLE contact_reasons_countries DROP FOREIGN KEY contact_reasons_countries_ibfk_2;

-- Re-add the original foreign keys with ON DELETE CASCADE where applicable
ALTER TABLE `contact_reasons_countries`
    ADD CONSTRAINT `contact_reasons_countries_ibfk_1`
        FOREIGN KEY (`contact_reason_id`) REFERENCES `contact_reasons` (`id`) ON DELETE CASCADE;

ALTER TABLE `contact_reasons_countries`
    ADD CONSTRAINT `contact_reasons_countries_ibfk_2`
        FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE CASCADE;

ALTER TABLE stands
    ADD CONSTRAINT stands_ibfk_1
        FOREIGN KEY (country_id) REFERENCES countries (id) ON DELETE CASCADE;

ALTER TABLE agents
    ADD CONSTRAINT agents_ibfk_1
        FOREIGN KEY (stand_id) REFERENCES stands (id) ON DELETE SET NULL;