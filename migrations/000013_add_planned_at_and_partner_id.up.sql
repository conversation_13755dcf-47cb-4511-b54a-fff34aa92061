-- Add planned_at and partner_id fields to appointments table
-- This migration standardizes all appointments to use planned_at instead of created_at for scheduling

-- Add new fields
ALTER TABLE appointments ADD COLUMN planned_at TIMESTAMP NULL;
ALTER TABLE appointments ADD COLUMN partner_id INT NULL;

-- Migrate existing data: copy created_at to planned_at for all existing appointments
UPDATE appointments SET planned_at = created_at WHERE planned_at IS NULL;

-- Make planned_at NOT NULL after data migration
ALTER TABLE appointments MODIFY COLUMN planned_at TIMESTAMP NOT NULL;

-- Add indexes for better query performance
CREATE INDEX idx_appointments_planned_at ON appointments(planned_at);
CREATE INDEX idx_appointments_partner_id ON appointments(partner_id);
