-- ADD logic deletion columns
ALTER TABLE countries ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE;
ALTER TABLE contact_reasons ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE;
ALTER TABLE contact_reasons_countries ADD COLUMN is_deleted B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE;
ALTER TABLE stands ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE;
ALTER TABLE agents ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE;

-- Delete foreign keys restrictions
ALTER TABLE `contact_reasons_countries` DROP FOREIGN KEY `contact_reasons_countries_ibfk_1`;
ALTER TABLE `contact_reasons_countries` DROP FOREIGN KEY `contact_reasons_countries_ibfk_2`;
ALTER TABLE stands DROP FOREIGN KEY stands_ibfk_1;
ALTER TABLE agents DROP FOREIGN KEY agents_ibfk_1;

-- Add foreign keys restrictions without delete cascade
ALTER TABLE `contact_reasons_countries` ADD CONSTRAINT `contact_reasons_countries_ibfk_1` F<PERSON><PERSON><PERSON><PERSON> KEY (`contact_reason_id`) REFERENCES `contact_reasons` (`id`);
ALTER TABLE `contact_reasons_countries` ADD CONSTRAINT `contact_reasons_countries_ibfk_2` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`);
ALTER TABLE stands ADD CONSTRAINT stands_ibfk_1 FOREIGN KEY (country_id) REFERENCES countries (id);
ALTER TABLE agents ADD CONSTRAINT agents_ibfk_1 FOREIGN KEY (stand_id) REFERENCES stands (id) ON DELETE SET NULL;